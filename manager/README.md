# 目标检测管理器

多摄像头目标检测进程管理工具，基于Go语言开发，用于管理YOLO检测系统的多个检测进程。

## 功能特性

- ✅ **Web管理界面**: 现代化的Web界面，支持移动端
- ✅ **批量启动**: 根据配置文件批量启动所有检测进程
- ✅ **进程管理**: 启动、停止、监控目标检测进程
- ✅ **配置管理**: 读取、修改cameras_config.json配置文件
- ✅ **位置更新**: 动态修改摄像头location坐标字段
- ✅ **实时监控**: 实时查看所有检测进程运行状态
- ✅ **RESTful API**: 完整的API接口，支持第三方集成
- ✅ **配置验证**: 验证配置文件和依赖文件完整性

## 项目结构

```
manager/
├── main.go                    # 主程序入口
├── config.go                  # 配置文件管理
├── manager.go                 # 检测进程管理
├── web.go                     # Web服务器
├── go.mod                     # Go模块文件
├── README.md                  # 项目说明
├── detection_manager          # 编译后的可执行文件
├── config/
│   └── cameras_config.json    # 摄像头配置文件
├── runtime/
│   └── pids/                  # PID文件目录
└── web/                       # Web界面文件
    ├── index.html             # 主页面
    ├── style.css              # 样式文件
    └── script.js              # JavaScript文件
```

## 编译和运行

### 编译
```bash
cd manager
go build -o detection_manager
```

### 运行

#### Web管理界面（推荐）
```bash
# 启动Web管理界面
./detection_manager web

# 指定端口启动（默认8080）
./detection_manager web 9090
```

访问 http://localhost:8080 使用Web界面管理检测进程。

**Web界面功能：**
- 📊 实时系统状态监控
- 🎮 一键批量启动/停止所有检测进程
- 📹 摄像头卡片式管理界面
- ⚡ 单个摄像头启动/停止控制
- 📍 可视化位置坐标编辑
- 🔄 自动状态刷新（每5秒）
- 📱 响应式设计，支持移动端
- 🎨 现代化UI设计

#### 命令行界面
```bash
# 查看帮助
./detection_manager

# 批量启动所有检测进程
./detection_manager start-all

# 启动指定摄像头的检测
./detection_manager start camera_001

# 查看状态
./detection_manager status

# 停止指定摄像头的检测
./detection_manager stop camera_001

# 停止所有检测进程
./detection_manager stop-all

# 更新摄像头位置坐标
./detection_manager update-location camera_001 150 250

# 显示配置信息
./detection_manager config

# 验证配置文件
./detection_manager validate
```

## 配置文件

程序读取 `./config/cameras_config.json` 配置文件，格式如下：

```json
{
  "cameras": [
    {
      "id": "camera_001",
      "name": "主检测摄像头",
      "input": "rtsp://192.168.23.156:8554/jindian",
      "location": [100, 200]
    },
    {
      "id": "camera_002",
      "name": "辅助检测摄像头", 
      "input": "rtsp://admin:Yu19991222@@192.168.24.89:554/Streaming/Channels/2",
      "location": [300, 400]
    }
  ]
}
```

## 依赖文件

程序依赖以下文件（相对于manager目录）：
- `../build/yolo_cpp_multi` - YOLO检测程序
- `../model/RK3588/yolov8n.rknn` - YOLO模型文件
- `../model/coco_80_labels_list.txt` - 标签文件
- `../lib/` - 运行时库目录

## 检测参数

程序使用以下固定参数启动检测：
- 模型类型: YOLOv8
- 线程数: 15
- 读取引擎: ffmpeg (MPP硬解码)
- 2D加速: RGA硬件加速
- OpenCL: 关闭
- 显示FPS: 开启
- 详细输出: 开启

## 注意事项

1. 确保在manager目录下运行程序
2. 确保./config/cameras_config.json文件存在且格式正确
3. 确保检测程序和模型文件路径正确
4. 启动多个摄像头时会有2秒间隔，避免系统负载过高
5. 程序会自动监控进程状态，异常退出时会显示错误信息
6. PID文件保存在./runtime/pids/目录下

## 故障排除

### 常见问题

**Q: 提示"配置文件不存在"**
```bash
# 检查配置文件路径
ls -la ./config/cameras_config.json
```

**Q: 提示"检测程序不存在"**
```bash
# 检查检测程序是否编译
ls -la ../build/yolo_cpp_multi
# 如果不存在，需要先编译检测程序
cd .. && ./build.sh
```

**Q: 摄像头启动失败**
```bash
# 验证配置和依赖
./camera_manager validate
# 检查摄像头输入源是否可访问
```

**Q: 进程异常退出**
- 检查摄像头输入源连接
- 检查系统资源使用情况
- 查看详细错误信息
