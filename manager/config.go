package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"time"
)

// Region 检测区域结构
type Region struct {
	ID       string    `json:"id"`
	Name     string    `json:"name"`
	DeviceID string    `json:"device_id"` // 设备ID
	Points   [][]int   `json:"points"`    // 多边形顶点坐标 [[x1, y1], [x2, y2], ...]
}

// Camera 摄像头配置结构
type Camera struct {
	ID                 string              `json:"id"`
	Name               string              `json:"name"`
	Input              string              `json:"input"`
	Regions            []Region            `json:"regions"`            // 检测区域列表
	GroundCalibration  GroundCalibration   `json:"ground_calibration"` // 平面标定配置
	AnchorPoints       []AnchorPoint       `json:"anchor_points"`      // 锚点标定列表
}

// GroundCalibration 平面标定配置结构
type GroundCalibration struct {
	Enabled    bool               `json:"enabled"`    // 是否启用平面标定
	Rectangles []GroundRectangle  `json:"rectangles"` // 平面标定矩形列表
}

// GroundRectangle 平面标定矩形结构
type GroundRectangle struct {
	ID          string     `json:"id"`          // 矩形ID
	Name        string     `json:"name"`        // 矩形名称
	Points      [][]int    `json:"points"`      // 矩形四个角点坐标
	Description string     `json:"description"` // 描述信息
}

// AnchorPoint 锚点标定
type AnchorPoint struct {
	ID    string `json:"id"`    // 锚点ID
	X     int    `json:"x"`     // X坐标
	Y     int    `json:"y"`     // Y坐标
	Order int    `json:"order"` // 顺序
}

// DetectionConfig 检测配置结构
type DetectionConfig struct {
	ConfidenceThreshold float64 `json:"confidence_threshold"`
	ContinuousCount     int     `json:"continuous_count"`
}

// ProgressiveCooldownConfig 渐进式冷却配置
type ProgressiveCooldownConfig struct {
	Enabled              bool    `json:"enabled"`
	ResetCooldownMinutes float64 `json:"reset_cooldown_minutes"`
}

// AlarmConfig 报警配置结构
type AlarmConfig struct {
	AlarmURL            string                     `json:"alarm_url"`
	AlarmHeader         map[string]string          `json:"alarm_header"`
	CooldownMinute      int                        `json:"cooldown_minute"`
	ProgressiveCooldown ProgressiveCooldownConfig  `json:"progressive_cooldown"`
}

// LogConfig 日志配置结构
type LogConfig struct {
	EnableFile    bool   `json:"enable_file"`
	EnableConsole bool   `json:"enable_console"`
	MinLevel      string `json:"min_level"`
	RetentionDays int    `json:"retention_days"`
}

// ModelConfig 模型配置结构
type ModelConfig struct {
	ModelPath           string  `json:"model_path"`           // 模型文件路径
	LabelsPath          string  `json:"labels_path"`          // 标签文件路径
	ModelType           int     `json:"model_type"`           // 模型类型 (5=YOLOv5, 8=YOLOv8)
	ConfidenceThreshold float64 `json:"confidence_threshold"` // 置信度阈值
	Threads             int     `json:"threads"`              // 线程数
	UseOpenCL           bool    `json:"use_opencl"`           // 是否使用OpenCL
	UseRGA              bool    `json:"use_rga"`              // 是否使用RGA硬件加速
}

// Config 配置文件结构
type Config struct {
	Model     ModelConfig     `json:"model"`
	Cameras   []Camera        `json:"cameras"`
	Detection DetectionConfig `json:"detection"`
	Alarm     AlarmConfig     `json:"alarm"`
	Log       LogConfig       `json:"log"`
}

// ConfigManager 配置管理器
type ConfigManager struct {
	configPath string
	config     *Config
}

// NewConfigManager 创建配置管理器
func NewConfigManager(configPath string) *ConfigManager {
	return &ConfigManager{
		configPath: configPath,
	}
}

// LoadConfig 加载配置文件
func (cm *ConfigManager) LoadConfig() error {
	data, err := ioutil.ReadFile(cm.configPath)
	if err != nil {
		return fmt.Errorf("读取配置文件失败: %v", err)
	}

	cm.config = &Config{}
	err = json.Unmarshal(data, cm.config)
	if err != nil {
		return fmt.Errorf("解析配置文件失败: %v", err)
	}

	fmt.Printf("✓ 成功加载配置文件: %s\n", cm.configPath)
	fmt.Printf("✓ 发现 %d 个摄像头配置\n", len(cm.config.Cameras))
	return nil
}

// SaveConfig 保存配置文件
func (cm *ConfigManager) SaveConfig() error {
	data, err := json.MarshalIndent(cm.config, "", "  ")
	if err != nil {
		return fmt.Errorf("序列化配置失败: %v", err)
	}

	err = ioutil.WriteFile(cm.configPath, data, 0644)
	if err != nil {
		return fmt.Errorf("保存配置文件失败: %v", err)
	}

	fmt.Printf("✓ 配置文件已保存: %s\n", cm.configPath)
	return nil
}

// GetConfig 获取完整配置
func (cm *ConfigManager) GetConfig() *Config {
	return cm.config
}

// GetCameras 获取所有摄像头配置
func (cm *ConfigManager) GetCameras() []Camera {
	if cm.config == nil {
		return nil
	}
	return cm.config.Cameras
}

// GetCamera 根据ID获取摄像头配置
func (cm *ConfigManager) GetCamera(id string) (*Camera, error) {
	if cm.config == nil {
		return nil, fmt.Errorf("配置未加载")
	}

	for i := range cm.config.Cameras {
		if cm.config.Cameras[i].ID == id {
			return &cm.config.Cameras[i], nil
		}
	}
	return nil, fmt.Errorf("未找到摄像头: %s", id)
}

// UpdateLocation 更新摄像头位置（兼容接口，转换为单区域）
func (cm *ConfigManager) UpdateLocation(id string, x, y int) error {
	// 将单点位置转换为单个区域
	region := []int{x, y, x+50, y, x+50, y+50, x, y+50, 640, 480} // 创建一个50x50的矩形区域
	return cm.UpdateMultipleRegions(id, [][]int{region})
}

// AddCamera 添加新摄像头
func (cm *ConfigManager) AddCamera(camera Camera) error {
	if cm.config == nil {
		return fmt.Errorf("配置未加载")
	}

	// 检查ID是否已存在
	for _, existingCamera := range cm.config.Cameras {
		if existingCamera.ID == camera.ID {
			return fmt.Errorf("摄像头ID %s 已存在", camera.ID)
		}
	}

	// 添加到配置
	cm.config.Cameras = append(cm.config.Cameras, camera)

	fmt.Printf("✓ 添加摄像头: %s (%s)\n", camera.ID, camera.Name)

	return cm.SaveConfig()
}

// RemoveCamera 删除摄像头
func (cm *ConfigManager) RemoveCamera(id string) error {
	if cm.config == nil {
		return fmt.Errorf("配置未加载")
	}

	// 查找并删除摄像头
	for i, camera := range cm.config.Cameras {
		if camera.ID == id {
			// 删除元素
			cm.config.Cameras = append(cm.config.Cameras[:i], cm.config.Cameras[i+1:]...)

			fmt.Printf("✓ 删除摄像头: %s (%s)\n", id, camera.Name)

			return cm.SaveConfig()
		}
	}

	return fmt.Errorf("未找到摄像头: %s", id)
}

// UpdateCamera 更新摄像头配置
func (cm *ConfigManager) UpdateCamera(oldID string, updatedCamera Camera) error {
	if cm.config == nil {
		return fmt.Errorf("配置未加载")
	}

	// 查找并更新摄像头
	for i, camera := range cm.config.Cameras {
		if camera.ID == oldID {
			// 如果ID发生变化，需要检查新ID是否已存在
			if updatedCamera.ID != oldID {
				for _, existingCamera := range cm.config.Cameras {
					if existingCamera.ID == updatedCamera.ID {
						return fmt.Errorf("摄像头ID已存在: %s", updatedCamera.ID)
					}
				}
			}

			// 更新摄像头配置
			cm.config.Cameras[i] = updatedCamera

			fmt.Printf("✓ 更新摄像头: %s -> %s (%s)\n", oldID, updatedCamera.ID, updatedCamera.Name)

			return cm.SaveConfig()
		}
	}

	return fmt.Errorf("未找到摄像头: %s", oldID)
}

// UpdateRegion 更新摄像头检测区域
func (cm *ConfigManager) UpdateRegion(id string, region [][2]int, canvasWidth, canvasHeight int) error {
	camera, err := cm.GetCamera(id)
	if err != nil {
		return err
	}

	// 将区域数据存储到location字段（扩展格式）
	// 格式: [x1, y1, x2, y2, ..., canvasWidth, canvasHeight]
	regionData := make([]int, 0, len(region)*2+2)
	for _, point := range region {
		regionData = append(regionData, point[0], point[1])
	}
	regionData = append(regionData, canvasWidth, canvasHeight)

	// 转换为新的Region格式
	points := make([][]int, 0)
	for i := 0; i < len(regionData)-2; i += 2 {
		points = append(points, []int{regionData[i], regionData[i+1]})
	}

	newRegion := Region{
		ID:     fmt.Sprintf("region_%d", time.Now().Unix()),
		Name:   "检测区域",
		Points: points,
	}

	camera.Regions = []Region{newRegion}

	fmt.Printf("✓ 更新摄像头 %s 检测区域: %d个点, 画布尺寸: %dx%d\n",
		id, len(newRegion.Points), canvasWidth, canvasHeight)

	// 这里可以扩展Camera结构体来存储完整的区域数据
	// 目前先保存到配置文件的注释或扩展字段中

	return cm.SaveConfig()
}

// UpdateRegions 更新摄像头检测区域
func (cm *ConfigManager) UpdateRegions(id string, regions []Region) error {
	if cm.config == nil {
		return fmt.Errorf("配置未加载")
	}

	// 查找并更新摄像头
	for i, camera := range cm.config.Cameras {
		if camera.ID == id {
			cm.config.Cameras[i].Regions = regions
			fmt.Printf("✓ 更新摄像头 %s 的检测区域: %d 个区域\n", id, len(regions))
			return cm.SaveConfig()
		}
	}

	return fmt.Errorf("未找到摄像头: %s", id)
}

// UpdateGroundCalibration 更新摄像头平面标定配置
func (cm *ConfigManager) UpdateGroundCalibration(id string, groundCalibration GroundCalibration) error {
	if cm.config == nil {
		return fmt.Errorf("配置未加载")
	}

	// 查找并更新摄像头
	for i, camera := range cm.config.Cameras {
		if camera.ID == id {
			cm.config.Cameras[i].GroundCalibration = groundCalibration
			fmt.Printf("✓ 更新摄像头 %s 的平面标定配置: %d 个矩形\n", id, len(groundCalibration.Rectangles))
			return cm.SaveConfig()
		}
	}

	return fmt.Errorf("未找到摄像头: %s", id)
}

// UpdateMultipleRegions 更新摄像头多个检测区域（兼容旧格式）
func (cm *ConfigManager) UpdateMultipleRegions(id string, regions [][]int) error {
	camera, err := cm.GetCamera(id)
	if err != nil {
		return err
	}

	// 转换旧格式到新格式
	newRegions := make([]Region, 0)
	for i, regionData := range regions {
		points := make([][]int, 0)
		// 解析点坐标（忽略最后两个画布尺寸参数）
		for j := 0; j < len(regionData)-2; j += 2 {
			points = append(points, []int{regionData[j], regionData[j+1]})
		}

		region := Region{
			ID:     fmt.Sprintf("region_%d_%d", time.Now().Unix(), i),
			Name:   fmt.Sprintf("区域%d", i+1),
			Points: points,
		}
		newRegions = append(newRegions, region)
	}

	camera.Regions = newRegions

	fmt.Printf("✓ 更新摄像头 %s 检测区域: %d个区域\n", id, len(regions))

	return cm.SaveConfig()
}

// ValidateConfig 验证配置文件
func (cm *ConfigManager) ValidateConfig() error {
	if cm.config == nil {
		return fmt.Errorf("配置未加载")
	}

	if len(cm.config.Cameras) == 0 {
		return fmt.Errorf("配置文件中没有摄像头")
	}

	// 检查ID重复
	idMap := make(map[string]bool)
	for _, camera := range cm.config.Cameras {
		if camera.ID == "" {
			return fmt.Errorf("摄像头ID不能为空")
		}
		if camera.Input == "" {
			return fmt.Errorf("摄像头 %s 的输入源不能为空", camera.ID)
		}
		if idMap[camera.ID] {
			return fmt.Errorf("摄像头ID重复: %s", camera.ID)
		}
		idMap[camera.ID] = true
	}

	fmt.Printf("✓ 配置文件验证通过\n")
	return nil
}

// PrintConfig 打印配置信息
func (cm *ConfigManager) PrintConfig() {
	if cm.config == nil {
		fmt.Println("配置未加载")
		return
	}

	fmt.Println("\n=== 模型配置信息 ===")
	fmt.Printf("模型路径: %s\n", cm.config.Model.ModelPath)
	fmt.Printf("标签路径: %s\n", cm.config.Model.LabelsPath)
	fmt.Printf("模型类型: %d (YOLOv%d)\n", cm.config.Model.ModelType, cm.config.Model.ModelType)
	fmt.Printf("置信度阈值: %.2f\n", cm.config.Model.ConfidenceThreshold)
	fmt.Printf("线程数: %d\n", cm.config.Model.Threads)
	fmt.Printf("使用OpenCL: %t\n", cm.config.Model.UseOpenCL)
	fmt.Printf("使用RGA: %t\n", cm.config.Model.UseRGA)

	fmt.Println("\n=== 摄像头配置信息 ===")
	for _, camera := range cm.config.Cameras {
		fmt.Printf("ID: %s\n", camera.ID)
		fmt.Printf("  名称: %s\n", camera.Name)
		fmt.Printf("  输入: %s\n", camera.Input)
		fmt.Printf("  区域数量: %d\n", len(camera.Regions))
		fmt.Println()
	}
}
