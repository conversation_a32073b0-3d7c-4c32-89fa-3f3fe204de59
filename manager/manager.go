package main

import (
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"os/exec"
	"path/filepath"
	"strings"
	"syscall"
	"time"
)

// ProcessInfo 进程信息
type ProcessInfo struct {
	Camera    *Camera
	Cmd       *exec.Cmd
	PID       int
	Status    string // "running", "stopped", "error"
	StartTime time.Time
}

// PIDFileInfo PID文件信息
type PIDFileInfo struct {
	CameraID  string    `json:"camera_id"`
	PID       int       `json:"pid"`
	StartTime time.Time `json:"start_time"`
	Status    string    `json:"status"`
}

// CameraManager 目标检测管理器
type CameraManager struct {
	configManager *ConfigManager
	processes     map[string]*ProcessInfo
	basePath      string
	pidDir        string
}

// NewCameraManager 创建目标检测管理器
func NewCameraManager(configPath string) *CameraManager {
	// 获取当前目录作为基础路径
	basePath, _ := filepath.Abs("./")
	pidDir := filepath.Join(basePath, "runtime", "pids")

	// 创建PID目录
	os.MkdirAll(pidDir, 0755)

	cm := &CameraManager{
		configManager: NewConfigManager(configPath),
		processes:     make(map[string]*ProcessInfo),
		basePath:      basePath,
		pidDir:        pidDir,
	}

	// 加载现有的PID文件
	cm.loadPIDFiles()

	return cm
}

// LoadConfig 加载配置
func (cm *CameraManager) LoadConfig() error {
	return cm.configManager.LoadConfig()
}

// getPIDFilePath 获取PID文件路径
func (cm *CameraManager) getPIDFilePath(cameraID string) string {
	return filepath.Join(cm.pidDir, fmt.Sprintf("%s.pid", cameraID))
}

// savePIDFile 保存PID文件
func (cm *CameraManager) savePIDFile(cameraID string, pid int) error {
	pidInfo := PIDFileInfo{
		CameraID:  cameraID,
		PID:       pid,
		StartTime: time.Now(),
		Status:    "running",
	}

	data, err := json.MarshalIndent(pidInfo, "", "  ")
	if err != nil {
		return err
	}

	pidFile := cm.getPIDFilePath(cameraID)
	return ioutil.WriteFile(pidFile, data, 0644)
}

// removePIDFile 删除PID文件
func (cm *CameraManager) removePIDFile(cameraID string) error {
	pidFile := cm.getPIDFilePath(cameraID)
	return os.Remove(pidFile)
}

// loadPIDFiles 加载所有PID文件
func (cm *CameraManager) loadPIDFiles() {
	files, err := ioutil.ReadDir(cm.pidDir)
	if err != nil {
		return
	}

	for _, file := range files {
		if !strings.HasSuffix(file.Name(), ".pid") {
			continue
		}

		pidFile := filepath.Join(cm.pidDir, file.Name())
		data, err := ioutil.ReadFile(pidFile)
		if err != nil {
			continue
		}

		var pidInfo PIDFileInfo
		err = json.Unmarshal(data, &pidInfo)
		if err != nil {
			continue
		}

		// 检查进程是否还在运行
		if cm.isProcessRunning(pidInfo.PID) {
			cm.processes[pidInfo.CameraID] = &ProcessInfo{
				PID:       pidInfo.PID,
				Status:    "running",
				StartTime: pidInfo.StartTime,
			}
		} else {
			// 进程已经不存在，删除PID文件
			os.Remove(pidFile)
		}
	}
}

// isProcessRunning 检查进程是否在运行
func (cm *CameraManager) isProcessRunning(pid int) bool {
	process, err := os.FindProcess(pid)
	if err != nil {
		return false
	}

	// 发送信号0检查进程是否存在
	err = process.Signal(syscall.Signal(0))
	return err == nil
}

// buildCommand 构建检测程序命令
func (cm *CameraManager) buildCommand(camera *Camera) *exec.Cmd {
	// 检测程序路径（在上级目录）
	programPath := filepath.Join(cm.basePath, "../build", "yolo_cpp_multi")

	// 转换为绝对路径
	if absPath, err := filepath.Abs(programPath); err == nil {
		programPath = absPath
	}

	// 从配置中获取模型和标签路径
	config := cm.configManager.GetConfig()
	var modelPath, labelPath string
	var modelType, threads int
	var useOpenCL, useRGA bool

	if config != nil && config.Model.ModelPath != "" {
		// 使用配置文件中的路径
		modelPath = config.Model.ModelPath
		labelPath = config.Model.LabelsPath
		modelType = config.Model.ModelType
		threads = config.Model.Threads
		useOpenCL = config.Model.UseOpenCL
		useRGA = config.Model.UseRGA

		// 如果是相对路径，转换为绝对路径
		if !filepath.IsAbs(modelPath) {
			modelPath = filepath.Join(cm.basePath, "..", modelPath)
		}
		if !filepath.IsAbs(labelPath) {
			labelPath = filepath.Join(cm.basePath, "..", labelPath)
		}
	} else {
		// 使用默认路径（兼容旧配置）
		modelPath = filepath.Join(cm.basePath, "../model", "RK3588", "yolov8n.rknn")
		labelPath = filepath.Join(cm.basePath, "../model", "coco_80_labels_list.txt")
		modelType = 8
		threads = 8
		useOpenCL = false
		useRGA = false
	}
	
	// 构建基础命令参数
	args := []string{
		"-m", modelPath,                    // 模型路径
		"-i", camera.Input,                 // 输入源
		"-t", fmt.Sprintf("%d", threads),   // 线程数
		"-r", "ffmpeg",                     // 读取引擎
		"-y", fmt.Sprintf("%d", modelType), // YOLO模型类型
		"-l", labelPath,                    // 标签文件
		"-s",                               // 显示FPS
		"-v",                               // 详细输出
		"--instance_name", camera.ID,       // 实例名称（用于日志文件夹）
	}

	// 根据配置设置硬件加速选项
	if useRGA {
		args = append(args, "-a", "2") // 使用RGA硬件加速
	} else {
		args = append(args, "-a", "1") // 使用OpenCV软件处理
	}

	// 根据配置设置OpenCL选项
	if useOpenCL {
		args = append(args, "-c", "1") // 启用OpenCL
	} else {
		args = append(args, "-c", "0") // 关闭OpenCL
	}

	// 添加检测和报警参数
	if config != nil {
		// 检测参数
		if config.Detection.ConfidenceThreshold > 0 {
			args = append(args, "--confidence", fmt.Sprintf("%.2f", config.Detection.ConfidenceThreshold))
		}
		if config.Detection.ContinuousCount > 0 {
			args = append(args, "--continuous", fmt.Sprintf("%d", config.Detection.ContinuousCount))
		}

		// 报警参数
		if config.Alarm.AlarmURL != "" {
			args = append(args, "--alarm_url", config.Alarm.AlarmURL)
		}
		if config.Alarm.CooldownMinute > 0 {
			args = append(args, "--cooldown", fmt.Sprintf("%d", config.Alarm.CooldownMinute))
		}

		// 报警头参数
		if len(config.Alarm.AlarmHeader) > 0 {
			headerJSON, err := json.Marshal(config.Alarm.AlarmHeader)
			if err == nil {
				args = append(args, "--alarm_header", string(headerJSON))
			}
		} else {
			args = append(args, "--alarm_header", "{}")
		}

		// 渐进式冷却参数
		if config.Alarm.ProgressiveCooldown.Enabled && config.Alarm.ProgressiveCooldown.ResetCooldownMinutes > 0 {
			args = append(args, "--reset_cooldown_minutes", fmt.Sprintf("%.2f", config.Alarm.ProgressiveCooldown.ResetCooldownMinutes))
		}

		// 日志配置参数
		if config.Log.EnableFile {
			args = append(args, "--log_enable_file", "true")
		} else {
			args = append(args, "--log_enable_file", "false")
		}

		if config.Log.EnableConsole {
			args = append(args, "--log_enable_console", "true")
		} else {
			args = append(args, "--log_enable_console", "false")
		}

		if config.Log.MinLevel != "" {
			args = append(args, "--log_min_level", config.Log.MinLevel)
		}

		if config.Log.RetentionDays > 0 {
			args = append(args, "--log_retention_days", fmt.Sprintf("%d", config.Log.RetentionDays))
		}

		// 区域参数
		if len(camera.Regions) > 0 {
			regionsStr := cm.buildRegionsString(camera.Regions)
			args = append(args, "--regions", regionsStr)
			args = append(args, "--draw_regions")
		}
	}
	
	cmd := exec.Command(programPath, args...)
	cmd.Dir = filepath.Join(cm.basePath, "../")  // 设置工作目录为项目根目录

	// 设置进程分离，使其独立于父进程（移除Setsid避免权限问题）
	cmd.SysProcAttr = &syscall.SysProcAttr{
		Setpgid: true,  // 创建新的进程组
	}

	// 设置环境变量
	cmd.Env = os.Environ()
	libPath := filepath.Join(cm.basePath, "../lib")
	for i, env := range cmd.Env {
		if len(env) > 16 && env[:16] == "LD_LIBRARY_PATH=" {
			cmd.Env[i] = fmt.Sprintf("LD_LIBRARY_PATH=%s:%s", libPath, env[16:])
			break
		}
	}
	// 如果没有找到LD_LIBRARY_PATH，添加一个
	cmd.Env = append(cmd.Env, fmt.Sprintf("LD_LIBRARY_PATH=%s", libPath))

	return cmd
}

// buildRegionsString 构建区域参数字符串
func (cm *CameraManager) buildRegionsString(regions []Region) string {
	var regionParts []string

	for _, region := range regions {
		// 构建单个区域字符串: id,name,device_id,x1,y1,x2,y2,...
		var parts []string
		parts = append(parts, region.ID)
		parts = append(parts, region.Name)
		parts = append(parts, region.DeviceID)

		// 添加坐标点
		for _, point := range region.Points {
			if len(point) >= 2 {
				parts = append(parts, fmt.Sprintf("%d", point[0])) // x
				parts = append(parts, fmt.Sprintf("%d", point[1])) // y
			}
		}

		regionParts = append(regionParts, strings.Join(parts, ","))
	}

	return strings.Join(regionParts, ";")
}

// StartCamera 启动单个摄像头的检测进程
func (cm *CameraManager) StartCamera(id string) error {
	// 检查是否已经在运行
	if info, exists := cm.processes[id]; exists && info.Status == "running" {
		return fmt.Errorf("摄像头 %s 的检测进程已在运行中 (PID: %d)", id, info.PID)
	}

	// 获取摄像头配置
	camera, err := cm.configManager.GetCamera(id)
	if err != nil {
		return err
	}

	// 构建命令
	cmd := cm.buildCommand(camera)

	fmt.Printf("启动摄像头 %s (%s) 的检测进程...\n", camera.ID, camera.Name)
	fmt.Printf("输入源: %s\n", camera.Input)
	
	// 添加调试信息
	fmt.Printf("执行命令: %s\n", cmd.Path)
	fmt.Printf("工作目录: %s\n", cmd.Dir)
	fmt.Printf("参数: %v\n", cmd.Args)

	// 启动进程
	err = cmd.Start()
	if err != nil {
		return fmt.Errorf("启动摄像头 %s 失败: %v", id, err)
	}
	
	// 记录进程信息
	cm.processes[id] = &ProcessInfo{
		Camera:    camera,
		Cmd:       cmd,
		PID:       cmd.Process.Pid,
		Status:    "running",
		StartTime: time.Now(),
	}

	// 保存PID文件
	err = cm.savePIDFile(id, cmd.Process.Pid)
	if err != nil {
		fmt.Printf("警告: 保存PID文件失败: %v\n", err)
	}

	fmt.Printf("✓ 摄像头 %s 的检测进程启动成功 (PID: %d)\n", id, cmd.Process.Pid)

	// 启动监控goroutine
	go cm.monitorProcess(id)

	return nil
}

// StopCamera 停止单个摄像头的检测进程
func (cm *CameraManager) StopCamera(id string) error {
	info, exists := cm.processes[id]
	if !exists {
		return fmt.Errorf("摄像头 %s 的检测进程未在运行", id)
	}

	if info.Status != "running" {
		return fmt.Errorf("摄像头 %s 的检测进程状态为 %s，无法停止", id, info.Status)
	}

	fmt.Printf("停止摄像头 %s 的检测进程 (PID: %d)...\n", id, info.PID)

	// 获取进程对象
	var process *os.Process
	var err error

	if info.Cmd != nil && info.Cmd.Process != nil {
		// 如果有Cmd对象，使用它的Process
		process = info.Cmd.Process
	} else {
		// 如果没有Cmd对象（从PID文件恢复的进程），通过PID获取进程
		process, err = os.FindProcess(info.PID)
		if err != nil {
			return fmt.Errorf("找不到进程 %d: %v", info.PID, err)
		}
	}

	// 发送SIGTERM信号
	err = process.Signal(syscall.SIGTERM)
	if err != nil {
		// 如果SIGTERM失败，使用SIGKILL
		err = process.Kill()
		if err != nil {
			return fmt.Errorf("停止摄像头 %s 的检测进程失败: %v", id, err)
		}
	}

	// 等待进程结束（最多等待5秒）
	if info.Cmd != nil {
		go func() {
			info.Cmd.Wait()
		}()
	}

	// 等待一下确保进程结束
	time.Sleep(1 * time.Second)

	info.Status = "stopped"

	// 删除PID文件
	err = cm.removePIDFile(id)
	if err != nil {
		fmt.Printf("警告: 删除PID文件失败: %v\n", err)
	}

	// 从进程列表中删除
	delete(cm.processes, id)

	fmt.Printf("✓ 摄像头 %s 的检测进程已停止\n", id)
	return nil
}

// StartAll 启动所有摄像头的检测进程
func (cm *CameraManager) StartAll() error {
	cameras := cm.configManager.GetCameras()
	if len(cameras) == 0 {
		return fmt.Errorf("没有找到摄像头配置")
	}

	fmt.Printf("开始批量启动 %d 个检测进程...\n", len(cameras))

	startedCount := 0  // 启动尝试成功的数量
	failedCameras := []string{}  // 启动失败的摄像头列表

	for _, camera := range cameras {
		err := cm.StartCamera(camera.ID)
		if err != nil {
			fmt.Printf("✗ 启动摄像头 %s 的检测进程失败: %v\n", camera.ID, err)
			failedCameras = append(failedCameras, camera.ID)
		} else {
			startedCount++
			// 启动间隔，避免系统负载过高
			time.Sleep(2 * time.Second)
		}
	}

	// 等待一下，让进程稳定，然后统计实际运行的进程数
	fmt.Println("等待进程稳定...")
	time.Sleep(3 * time.Second)

	runningCount := 0
	for _, camera := range cameras {
		if info, exists := cm.processes[camera.ID]; exists && info.Status == "running" {
			runningCount++
		}
	}

	fmt.Printf("批量启动完成: 启动尝试 %d/%d, 当前运行 %d/%d\n",
		startedCount, len(cameras), runningCount, len(cameras))

	if len(failedCameras) > 0 {
		fmt.Printf("启动失败的摄像头: %v\n", failedCameras)
	}

	return nil
}

// StopAll 停止所有摄像头的检测进程
func (cm *CameraManager) StopAll() error {
	if len(cm.processes) == 0 {
		fmt.Println("没有运行中的检测进程")
		return nil
	}

	fmt.Printf("停止所有运行中的检测进程...\n")
	
	for id := range cm.processes {
		err := cm.StopCamera(id)
		if err != nil {
			fmt.Printf("✗ 停止摄像头 %s 的检测进程失败: %v\n", id, err)
		}
	}
	
	fmt.Println("✓ 所有检测进程已停止")
	return nil
}

// monitorProcess 监控进程状态
func (cm *CameraManager) monitorProcess(id string) {
	info := cm.processes[id]
	if info == nil {
		return
	}

	// 等待进程结束
	err := info.Cmd.Wait()

	if err != nil {
		info.Status = "error"
		fmt.Printf("✗ 摄像头 %s 的检测进程异常退出: %v\n", id, err)
	} else {
		info.Status = "stopped"
		fmt.Printf("摄像头 %s 的检测进程正常退出\n", id)
	}

	// 删除PID文件
	cm.removePIDFile(id)

	// 从进程列表中删除
	delete(cm.processes, id)
}

// GetStatus 获取所有检测进程状态
func (cm *CameraManager) GetStatus() {
	cameras := cm.configManager.GetCameras()

	fmt.Println("\n=== 检测进程状态 ===")
	for _, camera := range cameras {
		info, exists := cm.processes[camera.ID]
		if exists {
			uptime := time.Since(info.StartTime).Round(time.Second)
			fmt.Printf("%s (%s): %s (PID: %d, 运行时间: %v)\n",
				camera.ID, camera.Name, info.Status, info.PID, uptime)
		} else {
			fmt.Printf("%s (%s): stopped\n", camera.ID, camera.Name)
		}
	}
	fmt.Println()
}

// UpdateLocation 更新摄像头位置
func (cm *CameraManager) UpdateLocation(id string, x, y int) error {
	return cm.configManager.UpdateLocation(id, x, y)
}
