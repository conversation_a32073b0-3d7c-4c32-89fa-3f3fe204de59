package main

import (
	"fmt"
	"os"
	"os/exec"
	"path/filepath"
	"strconv"
	"strings"
	"syscall"
)

const (
	configPath = "./config/cameras_config.json"
)

// runInBackground 在后台运行程序
func runInBackground(args []string) error {
	// 获取当前可执行文件路径
	executable, err := os.Executable()
	if err != nil {
		return fmt.Errorf("获取可执行文件路径失败: %v", err)
	}

	// 创建日志目录
	logDir := "./runtime/logs"
	if err := os.MkdirAll(logDir, 0755); err != nil {
		return fmt.Errorf("创建日志目录失败: %v", err)
	}

	// 创建日志文件
	logFile := filepath.Join(logDir, "daemon.log")
	file, err := os.OpenFile(logFile, os.O_CREATE|os.O_WRONLY|os.O_APPEND, 0644)
	if err != nil {
		return fmt.Errorf("创建日志文件失败: %v", err)
	}
	defer file.Close()

	// 创建命令
	cmd := exec.Command(executable, args[1:]...)

	// 设置进程组，使其独立于父进程
	cmd.SysProcAttr = &syscall.SysProcAttr{
		Setpgid: true,
	}

	// 重定向输出到日志文件
	cmd.Stdout = file
	cmd.Stderr = file
	cmd.Stdin = nil

	// 启动进程
	if err := cmd.Start(); err != nil {
		return fmt.Errorf("启动后台进程失败: %v", err)
	}

	// 保存PID
	pidDir := "./runtime/pids"
	if err := os.MkdirAll(pidDir, 0755); err != nil {
		return fmt.Errorf("创建PID目录失败: %v", err)
	}

	pidFile := filepath.Join(pidDir, "daemon.pid")
	if err := savePID(pidFile, cmd.Process.Pid); err != nil {
		return fmt.Errorf("保存PID失败: %v", err)
	}

	fmt.Printf("后台进程已启动，PID: %d，日志文件: %s\n", cmd.Process.Pid, logFile)
	return nil
}

// savePID 保存进程ID到文件
func savePID(pidFile string, pid int) error {
	file, err := os.Create(pidFile)
	if err != nil {
		return err
	}
	defer file.Close()

	_, err = fmt.Fprintf(file, "%d", pid)
	return err
}

func main() {
	if len(os.Args) < 2 {
		printUsage()
		os.Exit(1)
	}

	// 检查是否为后台模式
	isDaemon := len(os.Args) > 2 && (os.Args[len(os.Args)-1] == "-d" || os.Args[len(os.Args)-1] == "--daemon")

	// 如果是后台模式，移除 -d 参数并重新启动
	if isDaemon {
		// 移除最后的 -d 参数
		newArgs := os.Args[:len(os.Args)-1]
		err := runInBackground(newArgs)
		if err != nil {
			fmt.Printf("后台启动失败: %v\n", err)
			os.Exit(1)
		}
		fmt.Println("已在后台启动")
		return
	}

	// 创建摄像头管理器
	manager := NewCameraManager(configPath)

	// 加载配置
	err := manager.LoadConfig()
	if err != nil {
		fmt.Printf("错误: %v\n", err)
		os.Exit(1)
	}

	// 解析命令
	command := os.Args[1]
	switch command {
	case "web":
		handleWeb(manager)
	case "stop-web":
		handleStopWeb(manager)
	case "start-all":
		handleStartAll(manager)
	case "stop-all":
		handleStopAll(manager)
	case "start":
		handleStart(manager)
	case "stop":
		handleStop(manager)
	case "status":
		handleStatus(manager)
	case "update-location":
		handleUpdateLocation(manager)
	case "config":
		handleConfig(manager)
	case "validate":
		handleValidate(manager)
	default:
		fmt.Printf("未知命令: %s\n", command)
		printUsage()
		os.Exit(1)
	}
}

func printUsage() {
	fmt.Println("目标检测管理器 - 多摄像头目标检测进程管理工具")
	fmt.Println()
	fmt.Println("用法:")
	fmt.Println("  detection_manager <command> [arguments] [-d]")
	fmt.Println()
	fmt.Println("命令:")
	fmt.Println("  web                                 启动Web管理界面")
	fmt.Println("  stop-web                            停止Web管理界面")
	fmt.Println("  start-all                           批量启动所有检测进程")
	fmt.Println("  stop-all                            停止所有检测进程")
	fmt.Println("  start <camera_id>                   启动指定摄像头的检测")
	fmt.Println("  stop <camera_id>                    停止指定摄像头的检测")
	fmt.Println("  status                              查看所有检测进程状态")
	fmt.Println("  update-location <camera_id> <x> <y> 更新摄像头位置坐标")
	fmt.Println("  config                              显示配置信息")
	fmt.Println("  validate                            验证配置文件")
	fmt.Println()
	fmt.Println("选项:")
	fmt.Println("  -d, --daemon                        后台运行模式")
	fmt.Println()
	fmt.Println("示例:")
	fmt.Println("  ./detection_manager start-all")
	fmt.Println("  ./detection_manager start camera_001")
	fmt.Println("  ./detection_manager web -d                    # 后台启动Web界面")
	fmt.Println("  ./detection_manager stop-web                 # 停止Web界面")
	fmt.Println("  ./detection_manager start-all -d             # 后台批量启动")
	fmt.Println("  ./detection_manager start camera_001 -d      # 后台启动单个摄像头")
	fmt.Println("  ./detection_manager update-location camera_001 150 250")
	fmt.Println("  ./detection_manager status")
}

func handleStartAll(manager *CameraManager) {
	fmt.Println("=== 批量启动检测进程 ===")
	err := manager.StartAll()
	if err != nil {
		fmt.Printf("错误: %v\n", err)
		os.Exit(1)
	}
}

func handleStopAll(manager *CameraManager) {
	fmt.Println("=== 停止所有检测进程 ===")
	err := manager.StopAll()
	if err != nil {
		fmt.Printf("错误: %v\n", err)
		os.Exit(1)
	}
}

func handleStart(manager *CameraManager) {
	if len(os.Args) < 3 {
		fmt.Println("错误: 请指定摄像头ID")
		fmt.Println("用法: detection_manager start <camera_id>")
		os.Exit(1)
	}

	cameraID := os.Args[2]
	fmt.Printf("=== 启动摄像头 %s 的检测进程 ===\n", cameraID)

	err := manager.StartCamera(cameraID)
	if err != nil {
		fmt.Printf("错误: %v\n", err)
		os.Exit(1)
	}
}

func handleStop(manager *CameraManager) {
	if len(os.Args) < 3 {
		fmt.Println("错误: 请指定摄像头ID")
		fmt.Println("用法: detection_manager stop <camera_id>")
		os.Exit(1)
	}

	cameraID := os.Args[2]
	fmt.Printf("=== 停止摄像头 %s 的检测进程 ===\n", cameraID)

	err := manager.StopCamera(cameraID)
	if err != nil {
		fmt.Printf("错误: %v\n", err)
		os.Exit(1)
	}
}

func handleStatus(manager *CameraManager) {
	manager.GetStatus()
}

func handleUpdateLocation(manager *CameraManager) {
	if len(os.Args) < 5 {
		fmt.Println("错误: 参数不足")
		fmt.Println("用法: detection_manager update-location <camera_id> <x> <y>")
		os.Exit(1)
	}

	cameraID := os.Args[2]
	x, err := strconv.Atoi(os.Args[3])
	if err != nil {
		fmt.Printf("错误: 无效的X坐标: %s\n", os.Args[3])
		os.Exit(1)
	}

	y, err := strconv.Atoi(os.Args[4])
	if err != nil {
		fmt.Printf("错误: 无效的Y坐标: %s\n", os.Args[4])
		os.Exit(1)
	}

	fmt.Printf("=== 更新摄像头 %s 位置坐标 ===\n", cameraID)
	err = manager.UpdateLocation(cameraID, x, y)
	if err != nil {
		fmt.Printf("错误: %v\n", err)
		os.Exit(1)
	}
}

func handleConfig(manager *CameraManager) {
	manager.configManager.PrintConfig()
}

func handleValidate(manager *CameraManager) {
	fmt.Println("=== 验证配置文件 ===")
	err := manager.configManager.ValidateConfig()
	if err != nil {
		fmt.Printf("配置验证失败: %v\n", err)
		os.Exit(1)
	}
	
	// 检查依赖文件是否存在
	fmt.Println("检查依赖文件...")

	basePath, _ := filepath.Abs("./")
	config := manager.configManager.GetConfig()

	files := []string{
		filepath.Join(basePath, "../build", "yolo_cpp_multi"),
	}

	// 添加配置文件中指定的模型和标签文件
	if config != nil && config.Model.ModelPath != "" {
		modelPath := config.Model.ModelPath
		labelPath := config.Model.LabelsPath

		// 如果是相对路径，转换为绝对路径
		if !filepath.IsAbs(modelPath) {
			modelPath = filepath.Join(basePath, "..", modelPath)
		}
		if !filepath.IsAbs(labelPath) {
			labelPath = filepath.Join(basePath, "..", labelPath)
		}

		files = append(files, modelPath, labelPath)
	} else {
		// 使用默认路径
		files = append(files,
			filepath.Join(basePath, "../model", "RK3588", "yolov8n.rknn"),
			filepath.Join(basePath, "../model", "coco_80_labels_list.txt"),
		)
	}
	
	allExists := true
	for _, file := range files {
		if _, err := os.Stat(file); os.IsNotExist(err) {
			fmt.Printf("✗ 文件不存在: %s\n", file)
			allExists = false
		} else {
			fmt.Printf("✓ 文件存在: %s\n", file)
		}
	}
	
	if allExists {
		fmt.Println("✓ 所有依赖文件检查通过")
	} else {
		fmt.Println("✗ 部分依赖文件缺失")
		os.Exit(1)
	}
}

func handleStopWeb(manager *CameraManager) {
	fmt.Println("=== 停止Web管理界面 ===")

	// 读取daemon PID文件
	pidFile := "./runtime/pids/daemon.pid"
	if _, err := os.Stat(pidFile); os.IsNotExist(err) {
		fmt.Println("Web服务未在后台运行")
		return
	}

	// 读取PID
	pidData, err := os.ReadFile(pidFile)
	if err != nil {
		fmt.Printf("读取PID文件失败: %v\n", err)
		return
	}

	pidStr := strings.TrimSpace(string(pidData))
	pid, err := strconv.Atoi(pidStr)
	if err != nil {
		fmt.Printf("无效的PID: %s\n", pidStr)
		return
	}

	// 检查进程是否存在
	process, err := os.FindProcess(pid)
	if err != nil {
		fmt.Printf("找不到进程 %d: %v\n", pid, err)
		// 清理PID文件
		os.Remove(pidFile)
		return
	}

	// 发送SIGTERM信号终止进程
	err = process.Signal(syscall.SIGTERM)
	if err != nil {
		fmt.Printf("终止进程失败: %v\n", err)
		return
	}

	fmt.Printf("✓ Web服务已停止 (PID: %d)\n", pid)

	// 清理PID文件
	os.Remove(pidFile)
}
