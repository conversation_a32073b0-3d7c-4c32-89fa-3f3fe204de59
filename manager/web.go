package main

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"os/exec"
	"regexp"
	"strings"
	"sync"
	"time"

	"github.com/gorilla/websocket"
)

// WebServer Web服务器
type WebServer struct {
	manager    *CameraManager
	port       string
	upgrader   websocket.Upgrader
	clients    map[*websocket.Conn]bool
	clientsMux sync.RWMutex
	statusCache *StatusCache
}

// StatusCache 状态缓存
type StatusCache struct {
	data      []CameraStatus
	timestamp time.Time
	mutex     sync.RWMutex
}

// APIResponse API响应结构
type APIResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
}

// CameraStatus 摄像头状态
type CameraStatus struct {
	ID                string            `json:"id"`
	Name              string            `json:"name"`
	Input             string            `json:"input"`
	Regions           []Region          `json:"regions"`
	GroundCalibration GroundCalibration `json:"ground_calibration"`
	Status            string            `json:"status"`
	PID               int               `json:"pid"`
	StartTime         time.Time         `json:"start_time"`
	Uptime            string            `json:"uptime"`
}

// NewWebServer 创建Web服务器
func NewWebServer(manager *CameraManager, port string) *WebServer {
	return &WebServer{
		manager: manager,
		port:    port,
		upgrader: websocket.Upgrader{
			CheckOrigin: func(r *http.Request) bool {
				return true // 允许跨域
			},
		},
		clients: make(map[*websocket.Conn]bool),
		statusCache: &StatusCache{},
	}
}

// Start 启动Web服务器
func (ws *WebServer) Start() error {
	// 静态文件服务
	http.Handle("/", http.FileServer(http.Dir("../web/")))

	// API路由
	http.HandleFunc("/api/cameras", ws.handleCameras)
	http.HandleFunc("/api/cameras/", ws.handleCameraAction)
	http.HandleFunc("/api/status", ws.handleStatus)
	http.HandleFunc("/api/config", ws.handleSystemConfig)
	http.HandleFunc("/api/frame", ws.handleFrame)
	http.HandleFunc("/api/instances/", ws.handleInstanceLogs)
	http.HandleFunc("/ws", ws.handleWebSocket)

	// 启动状态更新协程
	go ws.statusUpdateLoop()

	fmt.Printf("🌐 Web管理界面启动成功！\n")
	fmt.Printf("📱 访问地址: http://localhost:%s\n", ws.port)
	fmt.Printf("🔧 API文档: http://localhost:%s/api/status\n", ws.port)
	fmt.Printf("⚡ WebSocket: ws://localhost:%s/ws\n", ws.port)

	return http.ListenAndServe(":"+ws.port, nil)
}

// handleCameras 处理摄像头列表请求
func (ws *WebServer) handleCameras(w http.ResponseWriter, r *http.Request) {
	ws.setCORSHeaders(w)

	if r.Method == "OPTIONS" {
		return
	}

	switch r.Method {
	case "GET":
		ws.handleGetCameras(w, r)
	case "POST":
		ws.handleAddCamera(w, r)
	default:
		ws.sendError(w, "不支持的HTTP方法", http.StatusMethodNotAllowed)
	}
}

// handleGetCameras 获取摄像头列表
func (ws *WebServer) handleGetCameras(w http.ResponseWriter, r *http.Request) {
	// 使用缓存的状态数据
	statuses := ws.getCachedStatus()
	ws.sendSuccess(w, "获取摄像头列表成功", statuses)
}

// handleCameraAction 处理摄像头操作请求
func (ws *WebServer) handleCameraAction(w http.ResponseWriter, r *http.Request) {
	ws.setCORSHeaders(w)

	if r.Method == "OPTIONS" {
		return
	}

	// 解析URL路径
	path := strings.TrimPrefix(r.URL.Path, "/api/cameras/")
	parts := strings.Split(path, "/")

	// 添加调试日志
	fmt.Printf("🔍 URL解析: OriginalPath=%s, TrimmedPath=%s, Parts=%v, PartsCount=%d\n", r.URL.Path, path, parts, len(parts))

	// 处理批量操作
	if r.Method == "POST" && len(parts) == 1 {
		action := parts[0]
		if action == "start-all" {
			ws.handleBatchStart(w)
			return
		} else if action == "stop-all" {
			ws.handleBatchStop(w)
			return
		}
	}

	cameraID := parts[0]

	// 处理DELETE请求（直接删除摄像头）
	if r.Method == "DELETE" {
		ws.handleDeleteCamera(w, cameraID)
		return
	}

	// 处理PUT请求（直接修改摄像头信息）
	if r.Method == "PUT" && len(parts) == 1 {
		ws.handleUpdateCamera(w, r, cameraID)
		return
	}

	// 其他请求需要action参数
	if len(parts) < 2 {
		ws.sendError(w, "无效的请求路径", http.StatusBadRequest)
		return
	}

	action := parts[1]

	// 添加调试日志
	fmt.Printf("🔍 API请求: Method=%s, CameraID=%s, Action=%s, Parts=%v\n", r.Method, cameraID, action, parts)

	switch r.Method {
	case "POST":
		ws.handleCameraControl(w, cameraID, action)
	case "PUT":
		if action == "location" {
			ws.handleUpdateLocation(w, r, cameraID)
		} else if action == "region" {
			ws.handleUpdateRegion(w, r, cameraID)
		} else if action == "regions" {
			ws.handleUpdateRegions(w, r, cameraID)
		} else if action == "ground-calibration" {
			fmt.Printf("✅ 调用平面标定处理函数\n")
			ws.handleUpdateGroundCalibration(w, r, cameraID)
		} else {
			fmt.Printf("❌ 不支持的操作: %s\n", action)
			ws.sendError(w, "不支持的操作", http.StatusBadRequest)
		}
	default:
		ws.sendError(w, "不支持的HTTP方法", http.StatusMethodNotAllowed)
	}
}

// handleCameraControl 处理摄像头控制
func (ws *WebServer) handleCameraControl(w http.ResponseWriter, cameraID, action string) {
	switch action {
	case "start":
		err := ws.manager.StartCamera(cameraID)
		if err != nil {
			ws.sendError(w, fmt.Sprintf("启动失败: %v", err), http.StatusInternalServerError)
			return
		}
		ws.sendSuccess(w, fmt.Sprintf("摄像头 %s 启动成功", cameraID), nil)
		
	case "stop":
		err := ws.manager.StopCamera(cameraID)
		if err != nil {
			ws.sendError(w, fmt.Sprintf("停止失败: %v", err), http.StatusInternalServerError)
			return
		}
		ws.sendSuccess(w, fmt.Sprintf("摄像头 %s 停止成功", cameraID), nil)
		
	default:
		ws.sendError(w, "不支持的操作", http.StatusBadRequest)
	}
}

// handleUpdateLocation 处理位置更新
func (ws *WebServer) handleUpdateLocation(w http.ResponseWriter, r *http.Request, cameraID string) {
	var req struct {
		X int `json:"x"`
		Y int `json:"y"`
	}
	
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		ws.sendError(w, "无效的JSON数据", http.StatusBadRequest)
		return
	}
	
	err := ws.manager.UpdateLocation(cameraID, req.X, req.Y)
	if err != nil {
		ws.sendError(w, fmt.Sprintf("更新位置失败: %v", err), http.StatusInternalServerError)
		return
	}
	
	ws.sendSuccess(w, fmt.Sprintf("摄像头 %s 位置更新成功", cameraID), map[string]interface{}{
		"camera_id": cameraID,
		"location":  [2]int{req.X, req.Y},
	})
}

// handleStatus 处理状态请求
func (ws *WebServer) handleStatus(w http.ResponseWriter, r *http.Request) {
	ws.setCORSHeaders(w)
	
	if r.Method == "OPTIONS" {
		return
	}
	
	if r.Method != "GET" {
		ws.sendError(w, "只支持GET请求", http.StatusMethodNotAllowed)
		return
	}
	
	// 批量操作
	action := r.URL.Query().Get("action")
	if action != "" {
		switch action {
		case "start-all":
			err := ws.manager.StartAll()
			if err != nil {
				ws.sendError(w, fmt.Sprintf("批量启动失败: %v", err), http.StatusInternalServerError)
				return
			}
			ws.sendSuccess(w, "批量启动完成", nil)
			
		case "stop-all":
			err := ws.manager.StopAll()
			if err != nil {
				ws.sendError(w, fmt.Sprintf("批量停止失败: %v", err), http.StatusInternalServerError)
				return
			}
			ws.sendSuccess(w, "批量停止完成", nil)
			
		default:
			ws.sendError(w, "不支持的批量操作", http.StatusBadRequest)
		}
		return
	}
	
	// 返回系统状态
	cameras := ws.manager.configManager.GetCameras()
	runningCount := 0
	for _, camera := range cameras {
		if info, exists := ws.manager.processes[camera.ID]; exists && info.Status == "running" {
			runningCount++
		}
	}
	
	status := map[string]interface{}{
		"total_cameras":   len(cameras),
		"running_cameras": runningCount,
		"timestamp":       time.Now(),
	}
	
	ws.sendSuccess(w, "获取状态成功", status)
}

// handleConfig 处理配置请求
func (ws *WebServer) handleConfig(w http.ResponseWriter, r *http.Request) {
	ws.setCORSHeaders(w)
	
	if r.Method == "OPTIONS" {
		return
	}
	
	if r.Method != "GET" {
		ws.sendError(w, "只支持GET请求", http.StatusMethodNotAllowed)
		return
	}
	
	cameras := ws.manager.configManager.GetCameras()
	ws.sendSuccess(w, "获取配置成功", cameras)
}

// setCORSHeaders 设置CORS头
func (ws *WebServer) setCORSHeaders(w http.ResponseWriter) {
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
	w.Header().Set("Access-Control-Allow-Headers", "Content-Type, Authorization")
	w.Header().Set("Content-Type", "application/json")
}

// sendSuccess 发送成功响应
func (ws *WebServer) sendSuccess(w http.ResponseWriter, message string, data interface{}) {
	response := APIResponse{
		Success: true,
		Message: message,
		Data:    data,
	}
	json.NewEncoder(w).Encode(response)
}

// sendError 发送错误响应
func (ws *WebServer) sendError(w http.ResponseWriter, message string, statusCode int) {
	w.WriteHeader(statusCode)
	response := APIResponse{
		Success: false,
		Message: message,
	}
	json.NewEncoder(w).Encode(response)
}

// handleAddCamera 处理新增摄像头请求
func (ws *WebServer) handleAddCamera(w http.ResponseWriter, r *http.Request) {
	var req struct {
		ID   string `json:"id"`
		Name string `json:"name"`
		Input string `json:"input"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		ws.sendError(w, "无效的JSON数据", http.StatusBadRequest)
		return
	}

	// 验证必填字段
	if req.ID == "" || req.Name == "" || req.Input == "" {
		ws.sendError(w, "ID、名称和输入源不能为空", http.StatusBadRequest)
		return
	}

	// 检查ID是否已存在
	if _, err := ws.manager.configManager.GetCamera(req.ID); err == nil {
		ws.sendError(w, "摄像头ID已存在", http.StatusConflict)
		return
	}

	// 添加到配置
	err := ws.manager.configManager.AddCamera(Camera{
		ID:      req.ID,
		Name:    req.Name,
		Input:   req.Input,
		Regions: []Region{}, // 初始化为空区域数组
		GroundCalibration: GroundCalibration{
			Enabled:    false,
			Rectangles: []GroundRectangle{},
		}, // 初始化为空平面标定配置
	})

	if err != nil {
		ws.sendError(w, fmt.Sprintf("添加摄像头失败: %v", err), http.StatusInternalServerError)
		return
	}

	ws.sendSuccess(w, fmt.Sprintf("摄像头 %s 添加成功", req.ID), map[string]interface{}{
		"camera_id": req.ID,
		"name":      req.Name,
	})
}

// handleUpdateCamera 处理修改摄像头请求
func (ws *WebServer) handleUpdateCamera(w http.ResponseWriter, r *http.Request, cameraID string) {
	var req struct {
		ID    string `json:"id"`
		Name  string `json:"name"`
		Input string `json:"input"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		ws.sendError(w, "无效的JSON数据", http.StatusBadRequest)
		return
	}

	// 验证必填字段
	if req.ID == "" || req.Name == "" || req.Input == "" {
		ws.sendError(w, "ID、名称和输入源不能为空", http.StatusBadRequest)
		return
	}

	// 检查原摄像头是否存在
	originalCamera, err := ws.manager.configManager.GetCamera(cameraID)
	if err != nil {
		ws.sendError(w, "摄像头不存在", http.StatusNotFound)
		return
	}

	// 如果ID发生变化，检查新ID是否已存在
	if req.ID != cameraID {
		if _, err := ws.manager.configManager.GetCamera(req.ID); err == nil {
			ws.sendError(w, "新的摄像头ID已存在", http.StatusConflict)
			return
		}
	}

	// 如果摄像头正在运行，先停止它
	if info, exists := ws.manager.processes[cameraID]; exists && info.Status == "running" {
		err := ws.manager.StopCamera(cameraID)
		if err != nil {
			ws.sendError(w, fmt.Sprintf("停止摄像头失败: %v", err), http.StatusInternalServerError)
			return
		}
	}

	// 更新摄像头配置
	updatedCamera := Camera{
		ID:                req.ID,
		Name:              req.Name,
		Input:             req.Input,
		Regions:           originalCamera.Regions,           // 保持原有的区域配置
		GroundCalibration: originalCamera.GroundCalibration, // 保持原有的平面标定配置
	}

	err = ws.manager.configManager.UpdateCamera(cameraID, updatedCamera)
	if err != nil {
		ws.sendError(w, fmt.Sprintf("更新摄像头失败: %v", err), http.StatusInternalServerError)
		return
	}

	ws.sendSuccess(w, fmt.Sprintf("摄像头 %s 更新成功", req.ID), map[string]interface{}{
		"camera_id": req.ID,
		"name":      req.Name,
		"input":     req.Input,
	})
}

// handleDeleteCamera 处理删除摄像头请求
func (ws *WebServer) handleDeleteCamera(w http.ResponseWriter, cameraID string) {
	// 检查摄像头是否存在
	camera, err := ws.manager.configManager.GetCamera(cameraID)
	if err != nil {
		ws.sendError(w, "摄像头不存在", http.StatusNotFound)
		return
	}

	// 如果摄像头正在运行，先停止它
	if info, exists := ws.manager.processes[cameraID]; exists && info.Status == "running" {
		err := ws.manager.StopCamera(cameraID)
		if err != nil {
			ws.sendError(w, fmt.Sprintf("停止摄像头失败: %v", err), http.StatusInternalServerError)
			return
		}
	}

	// 从配置中删除
	err = ws.manager.configManager.RemoveCamera(cameraID)
	if err != nil {
		ws.sendError(w, fmt.Sprintf("删除摄像头失败: %v", err), http.StatusInternalServerError)
		return
	}

	ws.sendSuccess(w, fmt.Sprintf("摄像头 %s (%s) 删除成功", cameraID, camera.Name), nil)
}

// handleWebSocket 处理WebSocket连接
func (ws *WebServer) handleWebSocket(w http.ResponseWriter, r *http.Request) {
	conn, err := ws.upgrader.Upgrade(w, r, nil)
	if err != nil {
		log.Printf("WebSocket升级失败: %v", err)
		return
	}
	defer conn.Close()

	// 添加客户端
	ws.clientsMux.Lock()
	ws.clients[conn] = true
	ws.clientsMux.Unlock()

	// 发送当前状态
	ws.sendStatusToClient(conn)

	// 保持连接并处理ping/pong
	for {
		_, _, err := conn.ReadMessage()
		if err != nil {
			break
		}
	}

	// 移除客户端
	ws.clientsMux.Lock()
	delete(ws.clients, conn)
	ws.clientsMux.Unlock()
}

// statusUpdateLoop 状态更新循环
func (ws *WebServer) statusUpdateLoop() {
	ticker := time.NewTicker(3 * time.Second) // 3秒更新一次
	defer ticker.Stop()

	for range ticker.C {
		ws.updateStatusCache()
		ws.broadcastStatus()
	}
}

// updateStatusCache 更新状态缓存
func (ws *WebServer) updateStatusCache() {
	cameras := ws.manager.configManager.GetCameras()
	statuses := make([]CameraStatus, 0, len(cameras))

	for _, camera := range cameras {
		status := CameraStatus{
			ID:                camera.ID,
			Name:              camera.Name,
			Input:             camera.Input,
			Regions:           camera.Regions,
			GroundCalibration: camera.GroundCalibration,
			Status:            "stopped",
			PID:               0,
		}

		if info, exists := ws.manager.processes[camera.ID]; exists {
			status.Status = info.Status
			status.PID = info.PID
			status.StartTime = info.StartTime
			if info.Status == "running" {
				status.Uptime = time.Since(info.StartTime).Round(time.Second).String()
			}
		}

		statuses = append(statuses, status)
	}

	ws.statusCache.mutex.Lock()
	ws.statusCache.data = statuses
	ws.statusCache.timestamp = time.Now()
	ws.statusCache.mutex.Unlock()
}

// broadcastStatus 广播状态给所有客户端
func (ws *WebServer) broadcastStatus() {
	ws.statusCache.mutex.RLock()
	data := ws.statusCache.data
	ws.statusCache.mutex.RUnlock()

	message := map[string]interface{}{
		"type": "status_update",
		"data": data,
		"timestamp": time.Now(),
	}

	ws.clientsMux.RLock()
	defer ws.clientsMux.RUnlock()

	for client := range ws.clients {
		err := client.WriteJSON(message)
		if err != nil {
			// 连接已断开，将在下次清理时移除
			continue
		}
	}
}

// sendStatusToClient 发送状态给单个客户端
func (ws *WebServer) sendStatusToClient(conn *websocket.Conn) {
	ws.statusCache.mutex.RLock()
	data := ws.statusCache.data
	ws.statusCache.mutex.RUnlock()

	message := map[string]interface{}{
		"type": "status_update",
		"data": data,
		"timestamp": time.Now(),
	}

	conn.WriteJSON(message)
}

// getCachedStatus 获取缓存的状态
func (ws *WebServer) getCachedStatus() []CameraStatus {
	ws.statusCache.mutex.RLock()
	defer ws.statusCache.mutex.RUnlock()

	// 如果缓存超过5秒，强制更新
	if time.Since(ws.statusCache.timestamp) > 5*time.Second {
		ws.statusCache.mutex.RUnlock()
		ws.updateStatusCache()
		ws.statusCache.mutex.RLock()
	}

	return ws.statusCache.data
}

// handleFrame 处理获取画面请求
func (ws *WebServer) handleFrame(w http.ResponseWriter, r *http.Request) {
	ws.setCORSHeaders(w)

	if r.Method == "OPTIONS" {
		return
	}

	if r.Method != "POST" {
		ws.sendError(w, "只支持POST请求", http.StatusMethodNotAllowed)
		return
	}

	var req struct {
		RtspURL string `json:"rtsp_url"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		ws.sendError(w, "无效的JSON数据", http.StatusBadRequest)
		return
	}

	if req.RtspURL == "" {
		ws.sendError(w, "RTSP URL不能为空", http.StatusBadRequest)
		return
	}

	// 使用FFmpeg获取一帧画面
	frameData, err := ws.captureFrame(req.RtspURL)
	if err != nil {
		ws.sendError(w, fmt.Sprintf("获取画面失败: %v", err), http.StatusInternalServerError)
		return
	}

	// 设置响应头
	w.Header().Set("Content-Type", "image/jpeg")
	w.Header().Set("Content-Length", fmt.Sprintf("%d", len(frameData)))

	// 写入图像数据
	w.Write(frameData)
}

// captureFrame 使用FFmpeg捕获一帧画面
func (ws *WebServer) captureFrame(rtspURL string) ([]byte, error) {
	// 创建临时文件
	tmpFile := fmt.Sprintf("/tmp/frame_%d.jpg", time.Now().UnixNano())
	defer os.Remove(tmpFile)

	// 使用FFmpeg命令捕获一帧
	cmd := exec.Command("ffmpeg",
		"-i", rtspURL,
		"-vframes", "1",
		"-f", "image2",
		"-y",
		tmpFile,
	)

	// 设置超时
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()
	cmd = exec.CommandContext(ctx, cmd.Args[0], cmd.Args[1:]...)

	// 执行命令
	if err := cmd.Run(); err != nil {
		return nil, fmt.Errorf("FFmpeg执行失败: %v", err)
	}

	// 读取生成的图像文件
	frameData, err := os.ReadFile(tmpFile)
	if err != nil {
		return nil, fmt.Errorf("读取图像文件失败: %v", err)
	}

	return frameData, nil
}

// handleUpdateRegion 处理更新区域请求
func (ws *WebServer) handleUpdateRegion(w http.ResponseWriter, r *http.Request, cameraID string) {
	var req struct {
		Region      []map[string]int `json:"region"`
		CanvasWidth int              `json:"canvas_width"`
		CanvasHeight int             `json:"canvas_height"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		ws.sendError(w, "无效的JSON数据", http.StatusBadRequest)
		return
	}

	if len(req.Region) < 3 {
		ws.sendError(w, "区域至少需要3个点", http.StatusBadRequest)
		return
	}

	// 转换区域数据格式
	region := make([][2]int, len(req.Region))
	for i, point := range req.Region {
		region[i] = [2]int{point["x"], point["y"]}
	}

	// 更新配置中的区域数据
	err := ws.manager.configManager.UpdateRegion(cameraID, region, req.CanvasWidth, req.CanvasHeight)
	if err != nil {
		ws.sendError(w, fmt.Sprintf("更新区域失败: %v", err), http.StatusInternalServerError)
		return
	}

	ws.sendSuccess(w, fmt.Sprintf("检测实例 %s 的区域更新成功", cameraID), map[string]interface{}{
		"camera_id": cameraID,
		"region": region,
		"canvas_size": map[string]int{
			"width": req.CanvasWidth,
			"height": req.CanvasHeight,
		},
	})
}

// handleUpdateRegions 处理更新多区域请求
func (ws *WebServer) handleUpdateRegions(w http.ResponseWriter, r *http.Request, cameraID string) {
	var req struct {
		Regions []Region `json:"regions"`
	}

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		ws.sendError(w, "无效的JSON数据", http.StatusBadRequest)
		return
	}

	// 验证每个区域数据
	for i, region := range req.Regions {
		if region.ID == "" || region.Name == "" {
			ws.sendError(w, fmt.Sprintf("区域 %d 缺少ID或名称", i+1), http.StatusBadRequest)
			return
		}
		if len(region.Points) < 3 {
			ws.sendError(w, fmt.Sprintf("区域 %d 至少需要3个顶点", i+1), http.StatusBadRequest)
			return
		}
		// 验证每个顶点有两个坐标
		for j, point := range region.Points {
			if len(point) != 2 {
				ws.sendError(w, fmt.Sprintf("区域 %d 顶点 %d 坐标格式错误", i+1, j+1), http.StatusBadRequest)
				return
			}
		}
	}

	// 更新配置中的区域数据
	err := ws.manager.configManager.UpdateRegions(cameraID, req.Regions)
	if err != nil {
		ws.sendError(w, fmt.Sprintf("更新区域失败: %v", err), http.StatusInternalServerError)
		return
	}

	ws.sendSuccess(w, fmt.Sprintf("检测实例 %s 的 %d 个区域更新成功", cameraID, len(req.Regions)), map[string]interface{}{
		"camera_id": cameraID,
		"regions_count": len(req.Regions),
		"regions": req.Regions,
	})
}

// handleBatchStart 处理批量启动
func (ws *WebServer) handleBatchStart(w http.ResponseWriter) {
	cameras := ws.manager.configManager.GetCameras()
	successCount := 0
	failedCameras := make([]string, 0)

	for _, camera := range cameras {
		err := ws.manager.StartCamera(camera.ID)
		if err != nil {
			failedCameras = append(failedCameras, fmt.Sprintf("%s(%s)", camera.Name, err.Error()))
		} else {
			successCount++
		}
	}

	if len(failedCameras) == 0 {
		ws.sendSuccess(w, fmt.Sprintf("批量启动成功，共启动 %d 个摄像头", successCount), map[string]interface{}{
			"success_count": successCount,
			"total_count":   len(cameras),
		})
	} else {
		ws.sendSuccess(w, fmt.Sprintf("批量启动完成，成功 %d 个，失败 %d 个", successCount, len(failedCameras)), map[string]interface{}{
			"success_count": successCount,
			"failed_count":  len(failedCameras),
			"failed_cameras": failedCameras,
			"total_count":   len(cameras),
		})
	}
}

// handleBatchStop 处理批量停止
func (ws *WebServer) handleBatchStop(w http.ResponseWriter) {
	cameras := ws.manager.configManager.GetCameras()
	successCount := 0
	failedCameras := make([]string, 0)

	for _, camera := range cameras {
		err := ws.manager.StopCamera(camera.ID)
		if err != nil {
			failedCameras = append(failedCameras, fmt.Sprintf("%s(%s)", camera.Name, err.Error()))
		} else {
			successCount++
		}
	}

	if len(failedCameras) == 0 {
		ws.sendSuccess(w, fmt.Sprintf("批量停止成功，共停止 %d 个摄像头", successCount), map[string]interface{}{
			"success_count": successCount,
			"total_count":   len(cameras),
		})
	} else {
		ws.sendSuccess(w, fmt.Sprintf("批量停止完成，成功 %d 个，失败 %d 个", successCount, len(failedCameras)), map[string]interface{}{
			"success_count": successCount,
			"failed_count":  len(failedCameras),
			"failed_cameras": failedCameras,
			"total_count":   len(cameras),
		})
	}
}

// handleWeb 处理Web命令
func handleWeb(manager *CameraManager) {
	port := "8080"

	// 查找端口参数，跳过 -d 参数
	for i := 2; i < len(os.Args); i++ {
		arg := os.Args[i]
		// 跳过 -d 和 --daemon 参数
		if arg != "-d" && arg != "--daemon" {
			port = arg
			break
		}
	}

	webServer := NewWebServer(manager, port)
	log.Fatal(webServer.Start())
}

// handleInstanceLogs 处理实例日志查看请求
func (ws *WebServer) handleInstanceLogs(w http.ResponseWriter, r *http.Request) {
	ws.setCORSHeaders(w)

	if r.Method == "OPTIONS" {
		return
	}

	if r.Method != "GET" {
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
		return
	}

	// 解析URL路径: /api/instances/{instanceId}/logs
	path := strings.TrimPrefix(r.URL.Path, "/api/instances/")
	parts := strings.Split(path, "/")

	if len(parts) < 2 || parts[1] != "logs" {
		http.Error(w, "Invalid path", http.StatusBadRequest)
		return
	}

	instanceId := parts[0]
	date := r.URL.Query().Get("date")
	level := r.URL.Query().Get("level")

	if date == "" {
		date = time.Now().Format("2006-01-02")
	}

	// 构建日志文件路径（相对于项目根目录）
	logFile := fmt.Sprintf("../logs/%s/%s.log", instanceId, date)

	// 读取并过滤日志
	logs, err := ws.readAndFilterLogs(logFile, level)
	if err != nil {
		// 如果文件不存在，返回空日志而不是错误
		if os.IsNotExist(err) {
			logs = fmt.Sprintf("日期 %s 暂无日志记录", date)
		} else {
			http.Error(w, fmt.Sprintf("读取日志失败: %v", err), http.StatusInternalServerError)
			return
		}
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]interface{}{
		"logs": logs,
		"date": date,
		"level": level,
		"instance_id": instanceId,
	})
}

// readAndFilterLogs 读取并过滤日志文件
func (ws *WebServer) readAndFilterLogs(logFile, level string) (string, error) {
	// 检查文件是否存在
	if _, err := os.Stat(logFile); os.IsNotExist(err) {
		return "", err
	}

	// 读取文件内容
	content, err := os.ReadFile(logFile)
	if err != nil {
		return "", err
	}

	// 如果没有指定级别过滤，直接返回全部内容
	if level == "" {
		return string(content), nil
	}

	// 按行过滤日志级别
	lines := strings.Split(string(content), "\n")
	var filteredLines []string

	for _, line := range lines {
		if strings.TrimSpace(line) == "" {
			continue
		}

		// 检查文本格式的日志：[时间戳] [级别] [模块] 消息内容
		// 使用正则表达式匹配级别字段
		levelPattern := `\[[\d-: .]+\]\s*\[` + level + `\s*\]`
		matched, _ := regexp.MatchString(levelPattern, line)
		if matched {
			filteredLines = append(filteredLines, line)
		}
	}

	if len(filteredLines) == 0 {
		return fmt.Sprintf("未找到级别为 %s 的日志记录", level), nil
	}

	return strings.Join(filteredLines, "\n"), nil
}

// handleSystemConfig 处理系统配置请求
func (ws *WebServer) handleSystemConfig(w http.ResponseWriter, r *http.Request) {
	ws.setCORSHeaders(w)

	if r.Method == "OPTIONS" {
		return
	}

	switch r.Method {
	case "GET":
		ws.getSystemConfig(w, r)
	case "PUT":
		ws.updateSystemConfig(w, r)
	default:
		http.Error(w, "Method not allowed", http.StatusMethodNotAllowed)
	}
}

// getSystemConfig 获取系统配置
func (ws *WebServer) getSystemConfig(w http.ResponseWriter, r *http.Request) {
	config := ws.manager.configManager.config
	if config == nil {
		http.Error(w, "配置未加载", http.StatusInternalServerError)
		return
	}

	// 构建响应数据，只包含需要配置的项目
	response := map[string]interface{}{
		"detection": map[string]interface{}{
			"confidence_threshold": config.Detection.ConfidenceThreshold,
			"continuous_count":     config.Detection.ContinuousCount,
		},
		"alarm": map[string]interface{}{
			"alarm_url":      config.Alarm.AlarmURL,
			"alarm_header":   config.Alarm.AlarmHeader,
			"cooldown_minute": config.Alarm.CooldownMinute,
			"progressive_cooldown": map[string]interface{}{
				"enabled":                config.Alarm.ProgressiveCooldown.Enabled,
				"reset_cooldown_minutes": config.Alarm.ProgressiveCooldown.ResetCooldownMinutes,
			},
		},
		"log": map[string]interface{}{
			"retention_days": config.Log.RetentionDays,
		},
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(response)
}

// updateSystemConfig 更新系统配置
func (ws *WebServer) updateSystemConfig(w http.ResponseWriter, r *http.Request) {
	var updateData struct {
		Detection struct {
			ConfidenceThreshold float64 `json:"confidence_threshold"`
			ContinuousCount     int     `json:"continuous_count"`
		} `json:"detection"`
		Alarm struct {
			AlarmURL      string            `json:"alarm_url"`
			AlarmHeader   map[string]string `json:"alarm_header"`
			CooldownMinute int              `json:"cooldown_minute"`
			ProgressiveCooldown struct {
				Enabled               bool    `json:"enabled"`
				ResetCooldownMinutes  float64 `json:"reset_cooldown_minutes"`
			} `json:"progressive_cooldown"`
		} `json:"alarm"`
		Log struct {
			RetentionDays int `json:"retention_days"`
		} `json:"log"`
	}

	if err := json.NewDecoder(r.Body).Decode(&updateData); err != nil {
		http.Error(w, "Invalid JSON", http.StatusBadRequest)
		return
	}

	config := ws.manager.configManager.config
	if config == nil {
		http.Error(w, "配置未加载", http.StatusInternalServerError)
		return
	}

	// 更新配置
	config.Detection.ConfidenceThreshold = updateData.Detection.ConfidenceThreshold
	config.Detection.ContinuousCount = updateData.Detection.ContinuousCount
	config.Alarm.AlarmURL = updateData.Alarm.AlarmURL
	config.Alarm.AlarmHeader = updateData.Alarm.AlarmHeader
	config.Alarm.CooldownMinute = updateData.Alarm.CooldownMinute
	config.Alarm.ProgressiveCooldown.Enabled = updateData.Alarm.ProgressiveCooldown.Enabled
	config.Alarm.ProgressiveCooldown.ResetCooldownMinutes = updateData.Alarm.ProgressiveCooldown.ResetCooldownMinutes
	config.Log.RetentionDays = updateData.Log.RetentionDays

	// 保存配置到文件
	if err := ws.manager.configManager.SaveConfig(); err != nil {
		http.Error(w, fmt.Sprintf("保存配置失败: %v", err), http.StatusInternalServerError)
		return
	}

	w.Header().Set("Content-Type", "application/json")
	json.NewEncoder(w).Encode(map[string]string{
		"status":  "success",
		"message": "配置保存成功",
	})
}

// handleUpdateGroundCalibration 处理更新平面标定请求
func (ws *WebServer) handleUpdateGroundCalibration(w http.ResponseWriter, r *http.Request, cameraID string) {
	var req GroundCalibration

	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		ws.sendError(w, "无效的JSON数据", http.StatusBadRequest)
		return
	}

	// 验证平面标定数据
	for i, rectangle := range req.Rectangles {
		if rectangle.ID == "" || rectangle.Name == "" {
			ws.sendError(w, fmt.Sprintf("地面矩形 %d 缺少ID或名称", i+1), http.StatusBadRequest)
			return
		}
		if len(rectangle.Points) != 4 {
			ws.sendError(w, fmt.Sprintf("地面矩形 %d 必须有4个角点", i+1), http.StatusBadRequest)
			return
		}
		// 验证每个点的坐标
		for j, point := range rectangle.Points {
			if len(point) != 2 {
				ws.sendError(w, fmt.Sprintf("地面矩形 %d 的第 %d 个点坐标格式错误", i+1, j+1), http.StatusBadRequest)
				return
			}
		}
	}

	// 更新配置中的平面标定数据
	err := ws.manager.configManager.UpdateGroundCalibration(cameraID, req)
	if err != nil {
		ws.sendError(w, fmt.Sprintf("更新平面标定失败: %v", err), http.StatusInternalServerError)
		return
	}

	ws.sendSuccess(w, fmt.Sprintf("检测实例 %s 的平面标定更新成功", cameraID), map[string]interface{}{
		"camera_id":          cameraID,
		"enabled":            req.Enabled,
		"rectangles_count":   len(req.Rectangles),
		"ground_calibration": req,
	})
}
