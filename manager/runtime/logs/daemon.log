🔍 URL解析: OriginalPath=/api/cameras/stop-all, TrimmedPath=stop-all, Parts=[stop-all], PartsCount=1
停止摄像头 camera_001 的检测进程 (PID: 5530)...
✗ 摄像头 camera_001 的检测进程异常退出: wait: no child processes
警告: 删除PID文件失败: remove /home/<USER>/Documents/slag_dec/manager/runtime/pids/camera_001.pid: no such file or directory
✓ 摄像头 camera_001 的检测进程已停止
停止摄像头 camera_002 的检测进程 (PID: 5531)...
✗ 摄像头 camera_002 的检测进程异常退出: waitid: no child processes
警告: 删除PID文件失败: remove /home/<USER>/Documents/slag_dec/manager/runtime/pids/camera_002.pid: no such file or directory
✓ 摄像头 camera_002 的检测进程已停止
停止摄像头 camera_003 的检测进程 (PID: 5532)...
✗ 摄像头 camera_003 的检测进程异常退出: wait: no child processes
警告: 删除PID文件失败: remove /home/<USER>/Documents/slag_dec/manager/runtime/pids/camera_003.pid: no such file or directory
✓ 摄像头 camera_003 的检测进程已停止
停止摄像头 camera_004 的检测进程 (PID: 5535)...
✗ 摄像头 camera_004 的检测进程异常退出: waitid: no child processes
警告: 删除PID文件失败: remove /home/<USER>/Documents/slag_dec/manager/runtime/pids/camera_004.pid: no such file or directory
✓ 摄像头 camera_004 的检测进程已停止
🔍 URL解析: OriginalPath=/api/cameras/camera_001/start, TrimmedPath=camera_001/start, Parts=[camera_001 start], PartsCount=2
🔍 API请求: Method=POST, CameraID=camera_001, Action=start, Parts=[camera_001 start]
启动摄像头 camera_001 (主检测摄像头) 的检测进程...
输入源: rtsp://admin:Yu19991222@@192.168.24.89:554/Streaming/Channels/2
执行命令: /home/<USER>/Documents/slag_dec/build/yolo_cpp_multi
工作目录: /home/<USER>/Documents/slag_dec
参数: [/home/<USER>/Documents/slag_dec/build/yolo_cpp_multi -m /home/<USER>/Documents/slag_dec/model/RK3588/yolov8n.rknn -i rtsp://admin:Yu19991222@@192.168.24.89:554/Streaming/Channels/2 -t 8 -r ffmpeg -a 1 -c 0 -y 8 -l /home/<USER>/Documents/slag_dec/model/coco_80_labels_list.txt -s -v --instance_name camera_001 --confidence 0.30 --continuous 10 --alarm_url http://192.168.23.156:3001 --cooldown 20 --alarm_header {"tenant_key":"c05a8956e30ecdd484f049442b4de6d4"} --reset_cooldown_minutes 5.00 --log_enable_file true --log_enable_console true --log_min_level INFO --log_retention_days 7 --regions region_1753316810669,区域1,1891785413613481986,144,249,320,248,358,302,120,307 --draw_regions]
✓ 摄像头 camera_001 的检测进程启动成功 (PID: 117513)
