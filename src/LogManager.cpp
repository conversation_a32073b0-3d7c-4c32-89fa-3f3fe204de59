/*
 * @Author: <PERSON> RF
 * @Date: 2025-01-24 16:30:00
 * @LastEditors: Li RF
 * @LastEditTime: 2025-01-24 16:30:00
 * @Description: 日志管理器实现
 * Email: <EMAIL>
 * Copyright (c) 2025 Li RF, All Rights Reserved.
 */

#include "LogManager.hpp"
#include <iostream>
#include <iomanip>
#include <sstream>
#include <cstdarg>
#include <algorithm>
#include <sys/stat.h>
#include <vector>
#include <unistd.h>  // for getpid()
#include <dirent.h>  // for directory operations
#include <ctime>     // for time operations
#include <cerrno>    // for errno

// 静态成员初始化
LogManager* LogManager::instance = nullptr;
std::mutex LogManager::instance_mutex;

/**
 * @Description: 私有构造函数
 */
LogManager::LogManager(const AppConfig& app_config) 
    : config(app_config), should_stop(false), heartbeat_running(false) {
    
    // 生成实例标识符
    instance_identifier = generateInstanceId();
    
    // 创建日志目录
    if (config.log_enable_file) {
        // 使用mkdir创建目录（兼容性更好）
        mkdir("logs", 0755);

        // 创建实例专用目录
        createInstanceLogDir();
        // 清理过期日志
        cleanupOldLogs();
        
        openLogFile();
    }
    
    // 启动异步写入线程
    writer_thread = std::thread(&LogManager::writerThreadFunction, this);
    
    // 记录系统启动日志
    log(LogLevel::INFO, "日志管理器初始化完成 - 系统启动中");
    
    // 启动心跳日志（如果配置启用）
    startHeartbeat();
}

/**
 * @Description: 析构函数
 */
LogManager::~LogManager() {
    // 停止心跳
    stopHeartbeat();
    
    // 停止写入线程
    should_stop = true;
    queue_cv.notify_all();
    if (writer_thread.joinable()) {
        writer_thread.join();
    }
    
    // 关闭日志文件
    if (log_file.is_open()) {
        log_file.close();
    }
}

/**
 * @Description: 获取单例实例（首次调用）
 */
LogManager* LogManager::getInstance(const AppConfig& config) {
    std::lock_guard<std::mutex> lock(instance_mutex);
    if (instance == nullptr) {
        instance = new LogManager(config);
    }
    return instance;
}

/**
 * @Description: 获取单例实例（后续调用）
 */
LogManager* LogManager::getInstance() {
    std::lock_guard<std::mutex> lock(instance_mutex);
    return instance;
}

/**
 * @Description: 销毁单例实例
 */
void LogManager::destroyInstance() {
    std::lock_guard<std::mutex> lock(instance_mutex);
    if (instance != nullptr) {
        delete instance;
        instance = nullptr;
    }
}

/**
 * @Description: 记录日志
 */
void LogManager::log(LogLevel level, const std::string& message, const std::string& module) {
    if (!shouldLog(level)) {
        return;
    }
    
    std::string timestamp = getCurrentTimestamp();
    LogEntry entry(level, timestamp, module, message);
    
    // 添加到异步队列
    {
        std::lock_guard<std::mutex> lock(queue_mutex);
        log_queue.push(entry);
    }
    queue_cv.notify_one();
    
    // 更新心跳时间
    updateHeartbeat();
}

/**
 * @Description: 格式化日志记录
 */
void LogManager::logFormatted(LogLevel level, const std::string& module, const char* format, ...) {
    if (!shouldLog(level)) {
        return;
    }
    
    va_list args;
    va_start(args, format);
    
    // 计算所需缓冲区大小
    va_list args_copy;
    va_copy(args_copy, args);
    int size = vsnprintf(nullptr, 0, format, args_copy);
    va_end(args_copy);
    
    if (size > 0) {
        std::vector<char> buffer(size + 1);
        vsnprintf(buffer.data(), buffer.size(), format, args);
        log(level, std::string(buffer.data()), module);
    }
    
    va_end(args);
}

/**
 * @Description: 获取当前时间戳
 */
std::string LogManager::getCurrentTimestamp() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    auto ms = std::chrono::duration_cast<std::chrono::milliseconds>(
        now.time_since_epoch()) % 1000;
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d %H:%M:%S");
    ss << "." << std::setfill('0') << std::setw(3) << ms.count();
    return ss.str();
}

/**
 * @Description: 获取当前日期
 */
std::string LogManager::getCurrentDate() const {
    auto now = std::chrono::system_clock::now();
    auto time_t = std::chrono::system_clock::to_time_t(now);
    
    std::stringstream ss;
    ss << std::put_time(std::localtime(&time_t), "%Y-%m-%d");
    return ss.str();
}

/**
 * @Description: 日志级别转字符串
 */
std::string LogManager::levelToString(LogLevel level) const {
    switch (level) {
        case LogLevel::DEBUG: return "DEBUG";
        case LogLevel::INFO:  return "INFO";
        case LogLevel::WARN:  return "WARN";
        case LogLevel::ERROR: return "ERROR";
        default: return "UNKNOWN";
    }
}

/**
 * @Description: 日志级别转带颜色的字符串（用于控制台）
 */
std::string LogManager::levelToColorString(LogLevel level) const {
    switch (level) {
        case LogLevel::DEBUG: return "\033[36mDEBUG\033[0m"; // 青色
        case LogLevel::INFO:  return "\033[32mINFO \033[0m"; // 绿色
        case LogLevel::WARN:  return "\033[33mWARN \033[0m"; // 黄色
        case LogLevel::ERROR: return "\033[31mERROR\033[0m"; // 红色
        default: return "UNKNOWN";
    }
}

/**
 * @Description: 判断是否应该记录该级别的日志
 */
bool LogManager::shouldLog(LogLevel level) const {
    LogLevel min_level = stringToLogLevel(config.log_min_level);
    return static_cast<int>(level) >= static_cast<int>(min_level);
}

/**
 * @Description: 字符串转日志级别
 */
LogLevel LogManager::stringToLogLevel(const std::string& level_str) const {
    std::string upper_str = level_str;
    std::transform(upper_str.begin(), upper_str.end(), upper_str.begin(), ::toupper);
    
    if (upper_str == "DEBUG") return LogLevel::DEBUG;
    if (upper_str == "INFO") return LogLevel::INFO;
    if (upper_str == "WARN" || upper_str == "WARNING") return LogLevel::WARN;
    if (upper_str == "ERROR") return LogLevel::ERROR;
    
    return LogLevel::INFO; // 默认级别
}

/**
 * @Description: 打开日志文件
 */
void LogManager::openLogFile() {
    std::string date = getCurrentDate();
    
    // 如果日期改变，需要切换日志文件
    if (date != current_date) {
        if (log_file.is_open()) {
            log_file.close();
        }
        
        current_date = date;
        
        // 使用实例专用目录，文件名只包含日期
        current_log_file_path = instance_log_dir + "/" + date + ".log";
        
        log_file.open(current_log_file_path, std::ios::app);
        if (!log_file.is_open()) {
            std::cerr << "Failed to open log file: " << current_log_file_path << std::endl;
        }
    }
}

/**
 * @Description: 写入文件
 */
void LogManager::writeToFile(const LogEntry& entry) {
    if (!config.log_enable_file || !log_file.is_open()) {
        return;
    }
    
    // 检查是否需要切换日志文件
    openLogFile();
    
    // 使用文本格式：[时间戳] [级别] [模块] 消息内容
    std::string module = entry.module.empty() ? "System" : entry.module;
    log_file << "[" << entry.timestamp << "] "
             << "[" << std::setw(5) << std::left << levelToString(entry.level) << "] "
             << "[" << module << "] "
             << entry.message << std::endl;
    
    log_file.flush();
}

/**
 * @Description: 写入控制台
 */
void LogManager::writeToConsole(const LogEntry& entry) {
    if (!config.log_enable_console) {
        return;
    }
    
    std::string module = entry.module.empty() ? "System" : entry.module;
    std::cout << "[" << entry.timestamp << "] "
              << "[" << levelToColorString(entry.level) << "] "
              << "[" << module << "] "
              << entry.message << std::endl;
}

/**
 * @Description: 异步写入线程函数
 */
void LogManager::writerThreadFunction() {
    while (!should_stop) {
        std::unique_lock<std::mutex> lock(queue_mutex);
        queue_cv.wait(lock, [this] { return !log_queue.empty() || should_stop; });
        
        while (!log_queue.empty()) {
            LogEntry entry = log_queue.front();
            log_queue.pop();
            lock.unlock();
            
            // 写入文件和控制台
            writeToFile(entry);
            writeToConsole(entry);
            
            lock.lock();
        }
    }
}

/**
 * @Description: 启动心跳日志
 */
void LogManager::startHeartbeat() {
    if (heartbeat_running) {
        return;
    }
    
    heartbeat_running = true;
    last_heartbeat = std::chrono::steady_clock::now();
    heartbeat_thread = std::thread(&LogManager::heartbeatThreadFunction, this);
}

/**
 * @Description: 停止心跳日志
 */
void LogManager::stopHeartbeat() {
    heartbeat_running = false;
    if (heartbeat_thread.joinable()) {
        heartbeat_thread.join();
    }
}

/**
 * @Description: 更新心跳时间
 */
void LogManager::updateHeartbeat() {
    last_heartbeat = std::chrono::steady_clock::now();
}

/**
 * @Description: 心跳线程函数
 */
void LogManager::heartbeatThreadFunction() {
    while (heartbeat_running) {
        // 每小时检查一次
        std::this_thread::sleep_for(std::chrono::hours(1));
        
        if (!heartbeat_running) break;
        
        auto now = std::chrono::steady_clock::now();
        auto elapsed = std::chrono::duration_cast<std::chrono::minutes>(now - last_heartbeat);
        
        // 记录心跳日志
        std::stringstream ss;
        ss << "进程心跳检测 - 系统运行正常，上次活动时间: " 
           << elapsed.count() << " 分钟前";
        
        log(LogLevel::INFO, ss.str(), "HeartbeatLogger");
    }
}

/**
 * @Description: 强制刷新所有日志
 */
void LogManager::flush() {
    // 等待队列清空
    std::unique_lock<std::mutex> lock(queue_mutex);
    queue_cv.wait(lock, [this] { return log_queue.empty(); });
    
    // 刷新文件
    if (log_file.is_open()) {
        log_file.flush();
    }
}

/**
 * @Description: 生成实例标识符
 */
std::string LogManager::generateInstanceId() {
    std::string instance_id;
    
    if (!config.instance_name.empty()) {
        // 直接使用用户指定的实例名称（Go管理器传递的是camera_001格式）
        instance_id = config.instance_name;
    } else {
        // 使用进程ID作为实例标识符
        pid_t pid = getpid();
        instance_id = "pid" + std::to_string(pid);
    }
    
    return instance_id;
}

/**
 * @Description: 创建实例日志目录
 */
void LogManager::createInstanceLogDir() {
    instance_log_dir = "logs/" + instance_identifier;
    
    // 创建实例专用目录
    if (mkdir(instance_log_dir.c_str(), 0755) == 0) {
        std::cout << "创建实例日志目录: " << instance_log_dir << std::endl;
    } else if (errno != EEXIST) {
        std::cerr << "创建实例日志目录失败: " << instance_log_dir << std::endl;
    }
}

/**
 * @Description: 清理过期日志文件
 */
void LogManager::cleanupOldLogs() {
    if (config.log_retention_days <= 0) {
        return;
    }
    
    DIR* dir = opendir(instance_log_dir.c_str());
    if (!dir) {
        return;
    }
    
    // 计算过期时间点
    time_t now = time(nullptr);
    time_t cutoff_time = now - (config.log_retention_days * 24 * 60 * 60);
    
    struct dirent* entry;
    int deleted_count = 0;
    
    while ((entry = readdir(dir)) != nullptr) {
        std::string filename = entry->d_name;
        
        // 跳过 . 和 .. 目录
        if (filename == "." || filename == "..") {
            continue;
        }
        
        // 检查是否是日志文件（格式：YYYY-MM-DD.log）
        if (filename.length() == 14 && filename.substr(10) == ".log") {
            std::string date_str = filename.substr(0, 10);
            
            // 解析日期
            struct tm tm_date = {};
            if (strptime(date_str.c_str(), "%Y-%m-%d", &tm_date)) {
                time_t file_time = mktime(&tm_date);
                
                // 如果文件过期，删除它
                if (file_time < cutoff_time) {
                    std::string full_path = instance_log_dir + "/" + filename;
                    if (remove(full_path.c_str()) == 0) {
                        deleted_count++;
                        std::cout << "删除过期日志文件: " << full_path << std::endl;
                    }
                }
            }
        }
    }
    
    closedir(dir);
    
    if (deleted_count > 0) {
        std::cout << "清理完成，删除了 " << deleted_count << " 个过期日志文件" << std::endl;
    }
} 