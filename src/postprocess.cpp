// Copyright (c) 2021 by Rockchip Electronics Co., Ltd. All Rights Reserved.
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//     http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

#include "postprocess.h"

#include <math.h>
#include <stdint.h>
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <sys/time.h>

#include <set>
#include <vector>
#include <string>
#include <fstream>
#include <iostream>
#include <sstream>
#include "rknn_api.h"  // 添加RKNN API头文件

// 全局标签管理
static std::vector<std::string> g_labels;
static bool g_labels_loaded = false;
#define LABEL_NALE_TXT_PATH "./model/coco_80_labels_list.txt"

// static char *labels[OBJ_CLASS_NUM];

const int anchor0[6] = {10, 13, 16, 30, 33, 23};
const int anchor1[6] = {30, 61, 62, 45, 59, 119};
const int anchor2[6] = {116, 90, 156, 198, 373, 326};

inline static int clamp(float val, int min, int max) { return val > min ? (val < max ? val : max) : min; }
/*
char *readLine(FILE *fp, char *buffer, int *len)
{
	int ch;
	int i = 0;
	size_t buff_len = 0;

	buffer = (char *)malloc(buff_len + 1);
	if (!buffer)
		return NULL; // Out of memory

	while ((ch = fgetc(fp)) != '\n' && ch != EOF)
	{
		buff_len++;
		void *tmp = realloc(buffer, buff_len + 1);
		if (tmp == NULL)
		{
			free(buffer);
			return NULL; // Out of memory
		}
		buffer = (char *)tmp;

		buffer[i] = (char)ch;
		i++;
	}
	buffer[i] = '\0';

	*len = buff_len;

	// Detect end
	if (ch == EOF && (i == 0 || ferror(fp)))
	{
		free(buffer);
		return NULL;
	}
	return buffer;
}

int readLines(const char *fileName, char *lines[], int max_line)
{
	FILE *file = fopen(fileName, "r");
	char *s;
	int i = 0;
	int n = 0;

	if (file == NULL)
	{
		printf("Open %s fail!\n", fileName);
		return -1;
	}

	while ((s = readLine(file, s, &n)) != NULL)
	{
		lines[i++] = s;
		if (i >= max_line)
			break;
	}
	fclose(file);
	return i;
}
*/
/**
 * @Description: 读取文件的所有行并存储到 labels 中
 * @param {string&} fileName: 
 * @param {int} max_lines: 
 * @return {*}
 */
int readLines(const std::string& fileName, std::vector<std::string>& labels, int max_lines) {
    std::ifstream file(fileName);
    if (!file.is_open()) {
        std::cerr << "打开文件 " << fileName << " 失败！" << std::endl;
        return -1;
    }

    std::string line;
    int count = 0;
    while (std::getline(file, line) && count < max_lines) {
        labels.push_back(line);
        count++;
    }
    file.close();
    return count;
}

/**
 * @Description: 从文件中读取类别标签，并存储在全局数组 labels 中
 * @param {char} *locationFilename:
 * @param {char} *label:
 * @return {*}
 */
/*
int loadLabelName(const char *locationFilename, char *label[])
{
	printf("loadLabelName %s\n", locationFilename);
	readLines(locationFilename, label, OBJ_CLASS_NUM);
	return 0;
}*/
int loadLabelName(const std::string& locationFilename, std::vector<std::string>& labels) {
    // std::cout << "加载标签名称 " << locationFilename << std::endl;
    return readLines(locationFilename, labels, OBJ_CLASS_NUM);
}

/**
 * @brief 计算两个边界框的交并比（IoU）
 * 
 * @param xmin0 第一个框的左上x坐标
 * @param ymin0 第一个框的左上y坐标
 * @param xmax0 第一个框的右下x坐标
 * @param ymax0 第一个框的右下y坐标
 * @param xmin1 第二个框的左上x坐标
 * @param ymin1 第二个框的左上y坐标
 * @param xmax1 第二个框的右下x坐标
 * @param ymax1 第二个框的右下y坐标
 * @return float IoU值
 */
static float CalculateOverlap(float xmin0, float ymin0, float xmax0, float ymax0, float xmin1, float ymin1, float xmax1,
							  float ymax1)
{
	float w = fmax(0.f, fmin(xmax0, xmax1) - fmax(xmin0, xmin1) + 1.0);
	float h = fmax(0.f, fmin(ymax0, ymax1) - fmax(ymin0, ymin1) + 1.0);
	float i = w * h;
	float u = (xmax0 - xmin0 + 1.0) * (ymax0 - ymin0 + 1.0) + (xmax1 - xmin1 + 1.0) * (ymax1 - ymin1 + 1.0) - i;
	return u <= 0.f ? 0.f : (i / u);
}

/**
 * @Description: 非极大值抑制，用于去除重叠的检测框。它保留IoU低于阈值的框，从而确保每个对象只有一个最优的检测框
 * 				可以通过使用 OpenCL 进行优化
 * @return {*}
 */
static int nms(int validCount, std::vector<float> &outputLocations, std::vector<int> classIds, std::vector<int> &order,
			   int filterId, float threshold)
{
	for (int i = 0; i < validCount; ++i)
	{
		if (order[i] == -1 || classIds[i] != filterId)
		{
			continue;
		}
		int n = order[i];
		// 对于每个检测框，计算其与同类别其他检测框的IoU
		for (int j = i + 1; j < validCount; ++j)
		{
			int m = order[j];
			if (m == -1 || classIds[i] != filterId)
			{
				continue;
			}
			float xmin0 = outputLocations[n * 4 + 0];
			float ymin0 = outputLocations[n * 4 + 1];
			float xmax0 = outputLocations[n * 4 + 0] + outputLocations[n * 4 + 2];
			float ymax0 = outputLocations[n * 4 + 1] + outputLocations[n * 4 + 3];

			float xmin1 = outputLocations[m * 4 + 0];
			float ymin1 = outputLocations[m * 4 + 1];
			float xmax1 = outputLocations[m * 4 + 0] + outputLocations[m * 4 + 2];
			float ymax1 = outputLocations[m * 4 + 1] + outputLocations[m * 4 + 3];

			float iou = CalculateOverlap(xmin0, ymin0, xmax0, ymax0, xmin1, ymin1, xmax1, ymax1);

			// 如果IoU超过阈值，则抑制（标记为无效）该检测框
			if (iou > threshold)
			{
				order[j] = -1;
			}
		}
	}
	return 0;
}

/**
 * @Description: 对检测结果按置信度进行降序排序。这里实现的是快速排序的变体，用于逆序排列
 * @param {vector<float>} &input:
 * @param {int} left:
 * @param {int} right:
 * @param {vector<int>} &indices:
 * @return {*}
 */
static int quick_sort_indice_inverse(std::vector<float> &input, int left, int right, std::vector<int> &indices)
{
	float key;
	int key_index;
	int low = left;
	int high = right;
	if (left < right)
	{
		key_index = indices[left];
		key = input[left];
		while (low < high)
		{
			while (low < high && input[high] <= key)
			{
				high--;
			}
			input[low] = input[high];
			indices[low] = indices[high];
			while (low < high && input[low] >= key)
			{
				low++;
			}
			input[high] = input[low];
			indices[high] = indices[low];
		}
		input[low] = key;
		indices[low] = key_index;
		quick_sort_indice_inverse(input, left, low - 1, indices);
		quick_sort_indice_inverse(input, low + 1, right, indices);
	}
	return low;
}

static float sigmoid(float x) { return 1.0 / (1.0 + expf(-x)); }

static float unsigmoid(float y) { return -1.0 * logf((1.0 / y) - 1.0); }

inline static int32_t __clip(float val, float min, float max)
{
	float f = val <= min ? min : (val >= max ? max : val);
	return f;
}

/**
 * @Description: 在量化和反量化之间转换浮点数和定点数，这在嵌入式系统中很常见，以减少计算和存储开销
 * @param {float} f32:
 * @param {int32_t} zp:
 * @param {float} scale:
 * @return {*}
 */
static int8_t qnt_f32_to_affine(float f32, int32_t zp, float scale)
{
	float dst_val = (f32 / scale) + zp;
	int8_t res = (int8_t)__clip(dst_val, -128, 127);
	return res;
}

static float deqnt_affine_to_f32(int8_t qnt, int32_t zp, float scale) { return ((float)qnt - (float)zp) * scale; }

static int process(int8_t *input, int *anchor, int grid_h, int grid_w, int height, int width, int stride,
				   std::vector<float> &boxes, std::vector<float> &objProbs, std::vector<int> &classId, float threshold,
				   int32_t zp, float scale)
{
	int validCount = 0;
	int grid_len = grid_h * grid_w;
	int8_t thres_i8 = qnt_f32_to_affine(threshold, zp, scale);
	for (int a = 0; a < 3; a++)
	{
		for (int i = 0; i < grid_h; i++)
		{
			for (int j = 0; j < grid_w; j++)
			{
				int8_t box_confidence = input[(PROP_BOX_SIZE * a + 4) * grid_len + i * grid_w + j];
				if (box_confidence >= thres_i8)
				{
					int offset = (PROP_BOX_SIZE * a) * grid_len + i * grid_w + j;
					int8_t *in_ptr = input + offset;
					float box_x = (deqnt_affine_to_f32(*in_ptr, zp, scale)) * 2.0 - 0.5;
					float box_y = (deqnt_affine_to_f32(in_ptr[grid_len], zp, scale)) * 2.0 - 0.5;
					float box_w = (deqnt_affine_to_f32(in_ptr[2 * grid_len], zp, scale)) * 2.0;
					float box_h = (deqnt_affine_to_f32(in_ptr[3 * grid_len], zp, scale)) * 2.0;
					box_x = (box_x + j) * (float)stride;
					box_y = (box_y + i) * (float)stride;
					box_w = box_w * box_w * (float)anchor[a * 2];
					box_h = box_h * box_h * (float)anchor[a * 2 + 1];
					box_x -= (box_w / 2.0);
					box_y -= (box_h / 2.0);

					int8_t maxClassProbs = in_ptr[5 * grid_len];
					int maxClassId = 0;
					for (int k = 1; k < OBJ_CLASS_NUM; ++k)
					{
						int8_t prob = in_ptr[(5 + k) * grid_len];
						if (prob > maxClassProbs)
						{
							maxClassId = k;
							maxClassProbs = prob;
						}
					}
					if (maxClassProbs > thres_i8)
					{
						objProbs.push_back((deqnt_affine_to_f32(maxClassProbs, zp, scale)) * (deqnt_affine_to_f32(box_confidence, zp, scale)));
						classId.push_back(maxClassId);
						validCount++;
						boxes.push_back(box_x);
						boxes.push_back(box_y);
						boxes.push_back(box_w);
						boxes.push_back(box_h);
					}
				}
			}
		}
	}
	return validCount;
}

/**
 * @Description: 接收模型的原始输出（量化后的数据），对其进行解码，并应用NMS和其他过滤逻辑，最终生成可读的检测结果
 * @return {*}
 */
int post_process(int8_t *input0, int8_t *input1, int8_t *input2, int model_in_h, int model_in_w, float conf_threshold,
				 float nms_threshold, BOX_RECT pads, float scale_w, float scale_h, std::vector<int32_t> &qnt_zps,
				 std::vector<float> &qnt_scales, detect_result_group_t *group)
{
	static int init = -1;
	static std::vector<std::string> labels;
	if (init == -1)
	{
		int ret = 0;
		ret = loadLabelName(LABEL_NALE_TXT_PATH, labels);
		std::cout << "Labels size: " << labels.size() << std::endl;
		if (ret < 0) {
			return -1;
		}

		init = 0;
	}
	memset(group, 0, sizeof(detect_result_group_t));

	std::vector<float> filterBoxes;
	std::vector<float> objProbs;
	std::vector<int> classId;

	// 处理不同步幅的输出
	// YOLO模型通常有多个输出层，每个输出层负责不同尺度的检测。这里分别处理步幅为8、16和32的输出层。
	// stride 8
	int stride0 = 8;
	int grid_h0 = model_in_h / stride0;
	int grid_w0 = model_in_w / stride0;
	int validCount0 = 0;
	validCount0 = process(input0, (int *)anchor0, grid_h0, grid_w0, model_in_h, model_in_w, stride0, filterBoxes, objProbs,
						  classId, conf_threshold, qnt_zps[0], qnt_scales[0]);

	// stride 16
	int stride1 = 16;
	int grid_h1 = model_in_h / stride1;
	int grid_w1 = model_in_w / stride1;
	int validCount1 = 0;
	validCount1 = process(input1, (int *)anchor1, grid_h1, grid_w1, model_in_h, model_in_w, stride1, filterBoxes, objProbs,
						  classId, conf_threshold, qnt_zps[1], qnt_scales[1]);

	// stride 32
	int stride2 = 32;
	int grid_h2 = model_in_h / stride2;
	int grid_w2 = model_in_w / stride2;
	int validCount2 = 0;
	validCount2 = process(input2, (int *)anchor2, grid_h2, grid_w2, model_in_h, model_in_w, stride2, filterBoxes, objProbs,
						  classId, conf_threshold, qnt_zps[2], qnt_scales[2]);

	int validCount = validCount0 + validCount1 + validCount2;
	// no object detect
	if (validCount <= 0)
	{
		return 0;
	}

	std::vector<int> indexArray;
	for (int i = 0; i < validCount; ++i)
	{
		indexArray.push_back(i);
	}

	// 按置信度降序排列检测结果
	quick_sort_indice_inverse(objProbs, 0, validCount - 1, indexArray);

	std::set<int> class_set(std::begin(classId), std::end(classId));

	// 对每个类别应用NMS，去除重叠的检测框
	for (auto c : class_set)
	{
		nms(validCount, filterBoxes, classId, indexArray, c, nms_threshold);
	}

	int last_count = 0;
	group->count = 0;
	/* box valid detect target */
	for (int i = 0; i < validCount; ++i)
	{
		if (indexArray[i] == -1 || last_count >= OBJ_NUMB_MAX_SIZE)
		{
			continue;
		}
		int n = indexArray[i];

		float x1 = filterBoxes[n * 4 + 0] - pads.left;
		float y1 = filterBoxes[n * 4 + 1] - pads.top;
		float x2 = x1 + filterBoxes[n * 4 + 2];
		float y2 = y1 + filterBoxes[n * 4 + 3];
		int id = classId[n];
		float obj_conf = objProbs[i];

		group->results[last_count].box.left = (int)(clamp(x1, 0, model_in_w) / scale_w);
		group->results[last_count].box.top = (int)(clamp(y1, 0, model_in_h) / scale_h);
		group->results[last_count].box.right = (int)(clamp(x2, 0, model_in_w) / scale_w);
		group->results[last_count].box.bottom = (int)(clamp(y2, 0, model_in_h) / scale_h);
		group->results[last_count].prop = obj_conf;
		// char *label = labels[id];
		// strncpy(group->results[last_count].name, labels[id].c_str(), OBJ_NAME_MAX_SIZE);
		// group->results[last_count].name[OBJ_NAME_MAX_SIZE - 1] = '\0';
		// std::cout << "id: " << id << ", labels.size(): " << labels.size() << std::endl;

		// 添加标签名称
		if (id >= 0 && id < labels.size()) {
			strncpy(group->results[last_count].name, labels[id].c_str(), OBJ_NAME_MAX_SIZE - 1);
			group->results[last_count].name[OBJ_NAME_MAX_SIZE - 1] = '\0';
		} else {
			group->results[last_count].name[0] = '\0'; // 设置为空字符串
			std::cerr << "Warning: id " << id << " is out of range (labels.size() = " << labels.size() << ")" << std::endl;
		}


		// printf("result %2d: (%4d, %4d, %4d, %4d), %s\n", i, group->results[last_count].box.left,
		// group->results[last_count].box.top,
		//        group->results[last_count].box.right, group->results[last_count].box.bottom, label);
		last_count++;
	}
	group->count = last_count;

	return 0;
}

// ==================== YOLOv8 后处理实现 ====================

static float CalculateOverlapYolov8(float xmin0, float ymin0, float xmax0, float ymax0,
                                     float xmin1, float ymin1, float xmax1, float ymax1)
{
    float w = fmax(0.f, fmin(xmax0, xmax1) - fmax(xmin0, xmin1) + 1.0);
    float h = fmax(0.f, fmin(ymax0, ymax1) - fmax(ymin0, ymin1) + 1.0);
    float i = w * h;
    float u = (xmax0 - xmin0 + 1.0) * (ymax0 - ymin0 + 1.0) + (xmax1 - xmin1 + 1.0) * (ymax1 - ymin1 + 1.0) - i;
    return u <= 0.f ? 0.f : (i / u);
}

static int nms_yolov8(int validCount, std::vector<float> &outputLocations, std::vector<int> classIds,
                      std::vector<int> &order, int filterId, float threshold)
{
    for (int i = 0; i < validCount; ++i)
    {
        if (order[i] == -1 || classIds[i] != filterId)
        {
            continue;
        }
        int n = order[i];
        for (int j = i + 1; j < validCount; ++j)
        {
            int m = order[j];
            if (m == -1 || classIds[i] != filterId)
            {
                continue;
            }
            float xmin0 = outputLocations[n * 4 + 0];
            float ymin0 = outputLocations[n * 4 + 1];
            float xmax0 = outputLocations[n * 4 + 0] + outputLocations[n * 4 + 2];
            float ymax0 = outputLocations[n * 4 + 1] + outputLocations[n * 4 + 3];

            float xmin1 = outputLocations[m * 4 + 0];
            float ymin1 = outputLocations[m * 4 + 1];
            float xmax1 = outputLocations[m * 4 + 0] + outputLocations[m * 4 + 2];
            float ymax1 = outputLocations[m * 4 + 1] + outputLocations[m * 4 + 3];

            float iou = CalculateOverlapYolov8(xmin0, ymin0, xmax0, ymax0, xmin1, ymin1, xmax1, ymax1);

            if (iou > threshold)
            {
                order[j] = -1;
            }
        }
    }
    return 0;
}

static int quick_sort_indice_inverse_yolov8(std::vector<float> &input, int left, int right, std::vector<int> &indices)
{
    float key;
    int key_index;
    int low = left;
    int high = right;
    if (left < right)
    {
        key_index = indices[left];
        key = input[left];
        while (low < high)
        {
            while (low < high && input[high] <= key)
            {
                high--;
            }
            input[low] = input[high];
            indices[low] = indices[high];
            while (low < high && input[low] >= key)
            {
                low++;
            }
            input[high] = input[low];
            indices[high] = indices[low];
        }
        input[low] = key;
        indices[low] = key_index;
        quick_sort_indice_inverse_yolov8(input, left, low - 1, indices);
        quick_sort_indice_inverse_yolov8(input, low + 1, right, indices);
    }
    return low;
}

static inline int32_t __clip_yolov8(float val, float min, float max)
{
    float f = val <= min ? min : (val >= max ? max : val);
    return f;
}

static int8_t qnt_f32_to_affine_yolov8(float f32, int32_t zp, float scale)
{
    float dst_val = (f32 / scale) + zp;
    int8_t res = (int8_t)__clip_yolov8(dst_val, -128, 127);
    return res;
}

static float deqnt_affine_to_f32_yolov8(int8_t qnt, int32_t zp, float scale)
{
    return ((float)qnt - (float)zp) * scale;
}

static void compute_dfl_yolov8(float* tensor, int dfl_len, float* box)
{
    for (int b = 0; b < 4; b++) {
        float exp_t[dfl_len];
        float exp_sum = 0;
        float acc_sum = 0;
        for (int i = 0; i < dfl_len; i++) {
            exp_t[i] = exp(tensor[i + b * dfl_len]);
            exp_sum += exp_t[i];
        }

        for (int i = 0; i < dfl_len; i++) {
            acc_sum += exp_t[i] / exp_sum * i;
        }
        box[b] = acc_sum;
    }
}

static int process_i8_yolov8(int8_t *box_tensor, int32_t box_zp, float box_scale,
                             int8_t *score_tensor, int32_t score_zp, float score_scale,
                             int8_t *score_sum_tensor, int32_t score_sum_zp, float score_sum_scale,
                             int grid_h, int grid_w, int stride, int dfl_len,
                             std::vector<float> &boxes,
                             std::vector<float> &objProbs,
                             std::vector<int> &classId,
                             float threshold)
{
    int validCount = 0;
    int grid_len = grid_h * grid_w;
    int8_t score_thres_i8 = qnt_f32_to_affine_yolov8(threshold, score_zp, score_scale);
    int8_t score_sum_thres_i8 = qnt_f32_to_affine_yolov8(threshold, score_sum_zp, score_sum_scale);

    for (int i = 0; i < grid_h; i++)
    {
        for (int j = 0; j < grid_w; j++)
        {
            int offset = i * grid_w + j;
            int max_class_id = -1;

            // 通过 score sum 起到快速过滤的作用
            if (score_sum_tensor != nullptr) {
                if (score_sum_tensor[offset] < score_sum_thres_i8) {
                    continue;
                }
            }

            int8_t max_score = -score_zp;
            int num_classes = get_num_classes();
            for (int c = 0; c < num_classes; c++) {
                if ((score_tensor[offset] > score_thres_i8) && (score_tensor[offset] > max_score))
                {
                    max_score = score_tensor[offset];
                    max_class_id = c;
                }
                offset += grid_len;
            }

            // compute box
            if (max_score > score_thres_i8) {
                offset = i * grid_w + j;
                float box[4];
                float before_dfl[dfl_len * 4];
                for (int k = 0; k < dfl_len * 4; k++) {
                    before_dfl[k] = deqnt_affine_to_f32_yolov8(box_tensor[offset], box_zp, box_scale);
                    offset += grid_len;
                }
                compute_dfl_yolov8(before_dfl, dfl_len, box);

                float x1, y1, x2, y2, w, h;
                x1 = (-box[0] + j + 0.5) * stride;
                y1 = (-box[1] + i + 0.5) * stride;
                x2 = (box[2] + j + 0.5) * stride;
                y2 = (box[3] + i + 0.5) * stride;
                w = x2 - x1;
                h = y2 - y1;
                boxes.push_back(x1);
                boxes.push_back(y1);
                boxes.push_back(w);
                boxes.push_back(h);

                objProbs.push_back(deqnt_affine_to_f32_yolov8(max_score, score_zp, score_scale));
                classId.push_back(max_class_id);
                validCount++;
            }
        }
    }
    return validCount;
}

static int process_fp32_yolov8(float *box_tensor, float *score_tensor, float *score_sum_tensor,
                               int grid_h, int grid_w, int stride, int dfl_len,
                               std::vector<float> &boxes,
                               std::vector<float> &objProbs,
                               std::vector<int> &classId,
                               float threshold)
{
    int validCount = 0;
    int grid_len = grid_h * grid_w;
    for (int i = 0; i < grid_h; i++)
    {
        for (int j = 0; j < grid_w; j++)
        {
            int offset = i * grid_w + j;
            int max_class_id = -1;

            // 通过 score sum 起到快速过滤的作用
            if (score_sum_tensor != nullptr) {
                if (score_sum_tensor[offset] < threshold) {
                    continue;
                }
            }

            float max_score = 0;
            int num_classes = get_num_classes();
            for (int c = 0; c < num_classes; c++) {
                if ((score_tensor[offset] > threshold) && (score_tensor[offset] > max_score))
                {
                    max_score = score_tensor[offset];
                    max_class_id = c;
                }
                offset += grid_len;
            }

            // compute box
            if (max_score > threshold) {
                offset = i * grid_w + j;
                float box[4];
                float before_dfl[dfl_len * 4];
                for (int k = 0; k < dfl_len * 4; k++) {
                    before_dfl[k] = box_tensor[offset];
                    offset += grid_len;
                }
                compute_dfl_yolov8(before_dfl, dfl_len, box);

                float x1, y1, x2, y2, w, h;
                x1 = (-box[0] + j + 0.5) * stride;
                y1 = (-box[1] + i + 0.5) * stride;
                x2 = (box[2] + j + 0.5) * stride;
                y2 = (box[3] + i + 0.5) * stride;
                w = x2 - x1;
                h = y2 - y1;
                boxes.push_back(x1);
                boxes.push_back(y1);
                boxes.push_back(w);
                boxes.push_back(h);

                objProbs.push_back(max_score);
                classId.push_back(max_class_id);
                validCount++;
            }
        }
    }
    return validCount;
}

/**
 * @Description: YOLOv8 后处理主函数
 * @param {void*} outputs: RKNN输出数据
 * @param {int} n_output: 输出数量
 * @param {std::vector<int32_t>&} output_zps: 量化零点
 * @param {std::vector<float>&} output_scales: 量化缩放因子
 * @param {std::vector<std::vector<int>>&} output_dims: 输出维度
 * @param {bool} is_quant: 是否量化模型
 * @param {int} model_in_h: 模型输入高度
 * @param {int} model_in_w: 模型输入宽度
 * @param {float} conf_threshold: 置信度阈值
 * @param {float} nms_threshold: NMS阈值
 * @param {float} scale_w: 宽度缩放比例
 * @param {float} scale_h: 高度缩放比例
 * @param {detect_result_group_t*} group: 检测结果
 * @return {int} 成功返回0
 */
int post_process_yolov8(void *outputs, int n_output,
                        std::vector<int32_t> &output_zps, std::vector<float> &output_scales,
                        std::vector<std::vector<int>> &output_dims, bool is_quant,
                        int model_in_h, int model_in_w,
                        float conf_threshold, float nms_threshold,
                        float scale_w, float scale_h,
                        detect_result_group_t *group)
{
    rknn_output *_outputs = (rknn_output *)outputs;

    std::vector<float> filterBoxes;
    std::vector<float> objProbs;
    std::vector<int> classId;
    int validCount = 0;
    int stride = 0;
    int grid_h = 0;
    int grid_w = 0;

    memset(group, 0, sizeof(detect_result_group_t));

    // YOLOv8默认3个分支
    int dfl_len = output_dims[0][1] / 4;  // DFL长度
    int output_per_branch = n_output / 3;

    for (int i = 0; i < 3; i++)
    {
        void *score_sum = nullptr;
        int32_t score_sum_zp = 0;
        float score_sum_scale = 1.0;

        if (output_per_branch == 3) {
            score_sum = _outputs[i * output_per_branch + 2].buf;
            score_sum_zp = output_zps[i * output_per_branch + 2];
            score_sum_scale = output_scales[i * output_per_branch + 2];
        }

        int box_idx = i * output_per_branch;
        int score_idx = i * output_per_branch + 1;

        grid_h = output_dims[box_idx][2];
        grid_w = output_dims[box_idx][3];
        stride = model_in_h / grid_h;

        if (is_quant)
        {
            validCount += process_i8_yolov8((int8_t *)_outputs[box_idx].buf, output_zps[box_idx], output_scales[box_idx],
                                            (int8_t *)_outputs[score_idx].buf, output_zps[score_idx], output_scales[score_idx],
                                            (int8_t *)score_sum, score_sum_zp, score_sum_scale,
                                            grid_h, grid_w, stride, dfl_len,
                                            filterBoxes, objProbs, classId, conf_threshold);
        }
        else
        {
            validCount += process_fp32_yolov8((float *)_outputs[box_idx].buf, (float *)_outputs[score_idx].buf, (float *)score_sum,
                                              grid_h, grid_w, stride, dfl_len,
                                              filterBoxes, objProbs, classId, conf_threshold);
        }
    }

    // no object detect
    if (validCount <= 0)
    {
        return 0;
    }

    std::vector<int> indexArray;
    for (int i = 0; i < validCount; ++i)
    {
        indexArray.push_back(i);
    }
    quick_sort_indice_inverse_yolov8(objProbs, 0, validCount - 1, indexArray);

    std::set<int> class_set(std::begin(classId), std::end(classId));

    for (auto c : class_set)
    {
        nms_yolov8(validCount, filterBoxes, classId, indexArray, c, nms_threshold);
    }

    int last_count = 0;
    group->count = 0;

    /* box valid detect target */
    for (int i = 0; i < validCount; ++i)
    {
        if (indexArray[i] == -1 || last_count >= OBJ_NUMB_MAX_SIZE) continue;

        int n = indexArray[i];

        float x1 = filterBoxes[n * 4 + 0];
        float y1 = filterBoxes[n * 4 + 1];
        float x2 = x1 + filterBoxes[n * 4 + 2];
        float y2 = y1 + filterBoxes[n * 4 + 3];
        int id = classId[n];
        float obj_conf = objProbs[i];

        group->results[last_count].box.left = (int)(clamp(x1, 0, model_in_w) / scale_w);
        group->results[last_count].box.top = (int)(clamp(y1, 0, model_in_h) / scale_h);
        group->results[last_count].box.right = (int)(clamp(x2, 0, model_in_w) / scale_w);
        group->results[last_count].box.bottom = (int)(clamp(y2, 0, model_in_h) / scale_h);
        group->results[last_count].prop = obj_conf;

        // 设置类别名称
        std::string label_name = get_label_name(id);
        snprintf(group->results[last_count].name, OBJ_NAME_MAX_SIZE, "%s", label_name.c_str());

        last_count++;
    }
    group->count = last_count;

    return 0;
}

// ==================== 标签管理功能 ====================

/**
 * @Description: 加载标签文件
 * @param {string&} labels_path: 标签文件路径
 * @return {int} 成功返回0，失败返回-1
 */
int load_labels(const std::string& labels_path) {
    g_labels.clear();
    g_labels_loaded = false;

    if (labels_path.empty()) {
        // 使用内置的COCO标签
        g_labels = {
            "person", "bicycle", "car", "motorcycle", "airplane", "bus", "train", "truck", "boat", "traffic light",
            "fire hydrant", "stop sign", "parking meter", "bench", "bird", "cat", "dog", "horse", "sheep", "cow",
            "elephant", "bear", "zebra", "giraffe", "backpack", "umbrella", "handbag", "tie", "suitcase", "frisbee",
            "skis", "snowboard", "sports ball", "kite", "baseball bat", "baseball glove", "skateboard", "surfboard",
            "tennis racket", "bottle", "wine glass", "cup", "fork", "knife", "spoon", "bowl", "banana", "apple",
            "sandwich", "orange", "broccoli", "carrot", "hot dog", "pizza", "donut", "cake", "chair", "couch",
            "potted plant", "bed", "dining table", "toilet", "tv", "laptop", "mouse", "remote", "keyboard", "cell phone",
            "microwave", "oven", "toaster", "sink", "refrigerator", "book", "clock", "vase", "scissors", "teddy bear",
            "hair drier", "toothbrush"
        };
        std::cout << "Using built-in COCO labels (" << g_labels.size() << " classes)" << std::endl;
    } else {
        // 从文件加载标签
        std::ifstream file(labels_path);
        if (!file.is_open()) {
            std::cerr << "Error: Cannot open labels file: " << labels_path << std::endl;
            return -1;
        }

        std::string line;
        while (std::getline(file, line)) {
            // 去除行末的换行符和空格
            line.erase(line.find_last_not_of(" \n\r\t") + 1);
            if (!line.empty()) {
                g_labels.push_back(line);
            }
        }
        file.close();

        if (g_labels.empty()) {
            std::cerr << "Error: Labels file is empty: " << labels_path << std::endl;
            return -1;
        }

        std::cout << "Loaded " << g_labels.size() << " labels from: " << labels_path << std::endl;
    }

    g_labels_loaded = true;
    return 0;
}

/**
 * @Description: 获取类别名称
 * @param {int} class_id: 类别ID
 * @return {string} 类别名称
 */
std::string get_label_name(int class_id) {
    if (!g_labels_loaded) {
        return "unknown";
    }

    if (class_id >= 0 && class_id < static_cast<int>(g_labels.size())) {
        return g_labels[class_id];
    }

    return "class_" + std::to_string(class_id);
}

/**
 * @Description: 获取类别数量
 * @return {int} 类别数量
 */
int get_num_classes() {
    if (!g_labels_loaded) {
        return OBJ_CLASS_NUM;  // 默认80个类别
    }
    return static_cast<int>(g_labels.size());
}

