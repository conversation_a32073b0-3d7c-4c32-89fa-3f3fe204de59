/*
 * @Author: <PERSON> RF
 * @Date: 2024-12-01 10:00:00
 * @LastEditors: Li RF
 * @LastEditTime: 2024-12-01 10:00:00
 * @Description: 平面标定管理器实现
 * Email: <EMAIL>
 * Copyright (c) 2024 Li RF, All Rights Reserved.
 */

#include "GroundCalibrationManager.hpp"
#include <iostream>
#include <algorithm>

using namespace std;
using namespace cv;

/**
 * @Description: 构造函数
 */
GroundCalibrationManager::GroundCalibrationManager(int img_width, int img_height) 
    : image_width(img_width), image_height(img_height), is_calibration_valid(false),
      real_world_min_x(0), real_world_max_x(0), real_world_min_y(0), real_world_max_y(0) {
    cout << "GroundCalibrationManager initialized with image size: " << img_width << "x" << img_height << endl;
}

/**
 * @Description: 析构函数
 */
GroundCalibrationManager::~GroundCalibrationManager() {
    // 清理资源
}

/**
 * @Description: 加载平面标定配置
 */
bool GroundCalibrationManager::loadCalibrationConfig(const GroundCalibration& config) {
    calibration_config = config;
    is_calibration_valid = false;
    
    if (!config.enabled || config.rectangles.empty()) {
        cout << "Ground calibration is disabled or no rectangles configured" << endl;
        return false;
    }
    
    // 使用第一个标定矩形计算透视变换矩阵
    const GroundRectangle& rectangle = config.rectangles[0];
    
    if (rectangle.points.size() != 4) {
        cout << "Invalid rectangle points count: " << rectangle.points.size() << endl;
        return false;
    }
    
    bool success = calculatePerspectiveMatrix(rectangle);
    if (success) {
        cout << "Ground calibration loaded successfully with rectangle: " << rectangle.name << endl;
    } else {
        cout << "Failed to calculate perspective matrix" << endl;
    }
    
    return success;
}

/**
 * @Description: 计算透视变换矩阵
 */
bool GroundCalibrationManager::calculatePerspectiveMatrix(const GroundRectangle& rectangle) {
    try {
        if (rectangle.points.size() != 4) {
            cout << "Invalid rectangle points count: " << rectangle.points.size() << endl;
            return false;
        }

        // 源点：标定矩形的四个角点（像素坐标）
        // 需要确保角点顺序正确：左上、右上、右下、左下
        vector<Point2f> src_points_raw;
        for (const auto& point : rectangle.points) {
            src_points_raw.push_back(Point2f(point.first, point.second));
        }

        // 自动排序角点：左上、右上、右下、左下
        vector<Point2f> src_points = sortRectanglePoints(src_points_raw);

        // 保存排序后的标定矩形角点
        calibration_corners = src_points;

        // 目标点：标准化的矩形（真实世界坐标）
        // 使用更大的坐标系统以提高精度
        vector<Point2f> dst_points = {
            Point2f(0, 0),        // 左上角
            Point2f(1000, 0),     // 右上角
            Point2f(1000, 1000),  // 右下角
            Point2f(0, 1000)      // 左下角
        };

        // 计算透视变换矩阵
        perspective_matrix = getPerspectiveTransform(src_points, dst_points);

        // 设置真实世界坐标范围
        real_world_min_x = 0;
        real_world_max_x = 1000;
        real_world_min_y = 0;
        real_world_max_y = 1000;

        is_calibration_valid = true;

        cout << "Perspective matrix calculated successfully" << endl;
        cout << "Calibration rectangle corners (sorted): ";
        for (const auto& pt : src_points) {
            cout << "(" << pt.x << "," << pt.y << ") ";
        }
        cout << endl;

        // 计算并显示标定矩形的关键信息
        float bottom_width = calculateGroundWidthAtY(calibration_corners[3].y);
        cout << "Calibration bottom width: " << bottom_width << endl;

        return true;

    } catch (const exception& e) {
        cout << "Error calculating perspective matrix: " << e.what() << endl;
        is_calibration_valid = false;
        return false;
    }
}

/**
 * @Description: 将像素坐标转换为真实世界坐标
 */
Point2f GroundCalibrationManager::pixelToRealWorld(const Point2f& pixel_point) {
    if (!is_calibration_valid) {
        return Point2f(0, 0);
    }
    
    vector<Point2f> src_points = {pixel_point};
    vector<Point2f> dst_points;
    
    perspectiveTransform(src_points, dst_points, perspective_matrix);
    
    return dst_points[0];
}

/**
 * @Description: 获取检测框底边的两个角点
 */
pair<Point2f, Point2f> GroundCalibrationManager::getBoxBottomCorners(const BOX_RECT& box) {
    Point2f left_bottom(box.left, box.bottom);
    Point2f right_bottom(box.right, box.bottom);
    return make_pair(left_bottom, right_bottom);
}

// 这些函数已不再使用，保留以防需要
// calculateHorizontalPosition 和 calculateWidthRatio 已被新的计算逻辑替代

/**
 * @Description: 计算目标位置信息
 */
TargetPositionInfo GroundCalibrationManager::calculateTargetPosition(const BOX_RECT& box,
                                                                    const string& object_name,
                                                                    float confidence) {
    TargetPositionInfo info;
    info.object_type = object_name;
    info.confidence = confidence;
    info.calibration_valid = is_calibration_valid;

    if (!is_calibration_valid) {
        // 如果没有有效的标定，使用基于像素的简单计算
        float center_x = (box.left + box.right) / 2.0f;
        float box_width = box.right - box.left;

        info.width_ratio = box_width / image_width;

        return info;
    }

    // 获取检测框底边的两个角点（像素坐标）
    auto bottom_corners = getBoxBottomCorners(box);
    Point2f left_bottom = bottom_corners.first;
    Point2f right_bottom = bottom_corners.second;

    // 计算目标底边的像素宽度和中心点
    float target_pixel_width = abs(right_bottom.x - left_bottom.x);
    float target_center_x = (left_bottom.x + right_bottom.x) / 2.0f;
    float target_y = left_bottom.y;  // 目标底边的Y坐标

    // 计算标定矩形在目标Y坐标处的宽度
    float calibration_width_at_y = calculateGroundWidthAtY(target_y);

    // 计算宽度占比：目标宽度 / 标定矩形在同一Y坐标处的宽度
    if (calibration_width_at_y > 0) {
        info.width_ratio = target_pixel_width / calibration_width_at_y;
    } else {
        info.width_ratio = 0.0f;
    }



    // 调试信息
    cout << "[GroundCalibration] Target: " << object_name << endl;
    cout << "  Target bottom: (" << left_bottom.x << "," << left_bottom.y << ") to ("
         << right_bottom.x << "," << right_bottom.y << ")" << endl;
    cout << "  Target pixel width: " << target_pixel_width << ", center X: " << target_center_x << endl;
    cout << "  Calibration width at Y=" << target_y << ": " << calibration_width_at_y << endl;
    cout << "  Width ratio: " << info.width_ratio << " (target/calibration)" << endl;

    return info;
}

/**
 * @Description: 对矩形角点进行排序（左上、右上、右下、左下）
 */
vector<Point2f> GroundCalibrationManager::sortRectanglePoints(const vector<Point2f>& points) {
    if (points.size() != 4) {
        return points;  // 如果不是4个点，直接返回
    }

    // 简化排序：先按Y坐标排序，再按X坐标排序
    vector<Point2f> sorted_points = points;

    // 按Y坐标排序，找出上面两个点和下面两个点
    sort(sorted_points.begin(), sorted_points.end(),
         [](const Point2f& a, const Point2f& b) { return a.y < b.y; });

    // 上面两个点：按X坐标排序（左上、右上）
    if (sorted_points[0].x > sorted_points[1].x) {
        swap(sorted_points[0], sorted_points[1]);
    }

    // 下面两个点：按X坐标排序（左下、右下）
    if (sorted_points[2].x > sorted_points[3].x) {
        swap(sorted_points[2], sorted_points[3]);
    }

    // 重新排列为：左上、右上、右下、左下
    vector<Point2f> result(4);
    result[0] = sorted_points[0];  // 左上
    result[1] = sorted_points[1];  // 右上
    result[2] = sorted_points[3];  // 右下
    result[3] = sorted_points[2];  // 左下

    return result;
}

/**
 * @Description: 计算标定矩形在指定Y坐标处的左右边界点
 */
pair<float, float> GroundCalibrationManager::getCalibrationBoundariesAtY(float pixel_y) {
    if (!is_calibration_valid || calibration_corners.size() != 4) {
        return make_pair(0.0f, 0.0f);
    }

    // 标定矩形角点：左上、右上、右下、左下
    Point2f top_left = calibration_corners[0];
    Point2f top_right = calibration_corners[1];
    Point2f bottom_right = calibration_corners[2];
    Point2f bottom_left = calibration_corners[3];

    // 计算左边界：从左上到左下的线性插值
    float left_x;
    if (abs(bottom_left.y - top_left.y) < 1e-6) {
        left_x = top_left.x;  // 水平线
    } else {
        float t = (pixel_y - top_left.y) / (bottom_left.y - top_left.y);
        t = max(0.0f, min(1.0f, t));  // 限制在0-1范围内
        left_x = top_left.x + t * (bottom_left.x - top_left.x);
    }

    // 计算右边界：从右上到右下的线性插值
    float right_x;
    if (abs(bottom_right.y - top_right.y) < 1e-6) {
        right_x = top_right.x;  // 水平线
    } else {
        float t = (pixel_y - top_right.y) / (bottom_right.y - top_right.y);
        t = max(0.0f, min(1.0f, t));  // 限制在0-1范围内
        right_x = top_right.x + t * (bottom_right.x - top_right.x);
    }

    return make_pair(left_x, right_x);
}



/**
 * @Description: 计算指定Y坐标处的地面宽度（像素坐标系）
 */
float GroundCalibrationManager::calculateGroundWidthAtY(float pixel_y) {
    auto boundaries = getCalibrationBoundariesAtY(pixel_y);
    return abs(boundaries.second - boundaries.first);
}

/**
 * @Description: 检查标定是否有效
 */
bool GroundCalibrationManager::isCalibrationValid() const {
    return is_calibration_valid;
}
