/**
 * @Description: 全局报警管理器实现
 * @Author: AI Assistant
 * @Date: 2025-01-24
 */

#include "GlobalAlarmManager.hpp"
#include "LogManager.hpp"
#include "GroundCalibrationManager.hpp"
#include <iostream>
#include <algorithm>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <ctime>
#include <curl/curl.h>
#include <string>
#include <cctype>

using namespace std;

// 静态成员初始化
GlobalAlarmManager* GlobalAlarmManager::instance = nullptr;
mutex GlobalAlarmManager::instance_mutex;

/**
 * @Description: HTTP响应回调函数
 */
static size_t WriteCallback(void *contents, size_t size, size_t nmemb, string *userp) {
    userp->append((char*)contents, size * nmemb);
    return size * nmemb;
}

/**
 * @Description: 私有构造函数
 */
GlobalAlarmManager::GlobalAlarmManager(const AppConfig& app_config) : config(app_config) {
    // 初始化所有区域的状态
    for (const auto& region : config.regions) {
        region_states[region.id] = GlobalAlarmState();
    }
    
    LOG_INFO_F("全局报警管理器初始化完成，配置区域数: %zu", config.regions.size());
    
    if (config.verbose) {
        cout << "GlobalAlarmManager initialized with " << config.regions.size() << " regions" << endl;
    }
}

/**
 * @Description: 获取全局报警管理器实例
 */
GlobalAlarmManager* GlobalAlarmManager::getInstance(const AppConfig& config) {
    lock_guard<mutex> lock(instance_mutex);
    if (instance == nullptr) {
        instance = new GlobalAlarmManager(config);
    }
    return instance;
}

/**
 * @Description: 获取全局报警管理器实例（已初始化后使用）
 */
GlobalAlarmManager* GlobalAlarmManager::getInstance() {
    lock_guard<mutex> lock(instance_mutex);
    return instance;
}

/**
 * @Description: 销毁全局报警管理器实例
 */
void GlobalAlarmManager::destroyInstance() {
    lock_guard<mutex> lock(instance_mutex);
    if (instance != nullptr) {
        delete instance;
        instance = nullptr;
    }
}

/**
 * @Description: 加载平面标定配置
 */
void GlobalAlarmManager::loadGroundCalibration(const GroundCalibration& calibration, int image_width, int image_height) {
    lock_guard<mutex> lock(states_mutex);

    // 创建平面标定管理器
    calibration_manager = make_unique<GroundCalibrationManager>(image_width, image_height);

    // 加载标定配置
    bool success = calibration_manager->loadCalibrationConfig(calibration);

    if (success) {
        LOG_INFO("平面标定配置加载成功");
        if (config.verbose) {
            cout << "Ground calibration loaded successfully" << endl;
        }
    } else {
        LOG_WARN("平面标定配置加载失败或未启用");
        if (config.verbose) {
            cout << "Ground calibration loading failed or disabled" << endl;
        }
    }
}

/**
 * @Description: 处理检测结果并判断是否需要报警（线程安全）
 */
vector<string> GlobalAlarmManager::processDetections(const detect_result_group_t& detections, cv::Mat& image) {
    lock_guard<mutex> lock(states_mutex);  // 确保线程安全
    
    vector<string> alarm_regions;
    auto current_time = chrono::steady_clock::now();
    
    // 为每个区域检查是否有检测目标
    for (const auto& region : config.regions) {
        bool has_detection_in_region = false;
        float max_confidence = 0.0f;

        // 检查所有检测结果，找到区域内置信度最高的目标
        for (int i = 0; i < detections.count; i++) {
            const detect_result_t& detection = detections.results[i];

            // 检查置信度是否达到阈值
            if (detection.prop >= config.confidence_threshold && isDetectionInRegion(detection, region)) {
                has_detection_in_region = true;
                max_confidence = max(max_confidence, detection.prop);

                if (config.verbose) {
                    pair<int, int> bottom_center = getBoxBottomCenter(detection.box);
                    cout << "[Thread-Safe] Detection found in region " << region.id
                         << " (" << region.name << "): "
                         << detection.name << " confidence=" << detection.prop
                         << " bottom_center=(" << bottom_center.first << "," << bottom_center.second << ")" << endl;
                }
            }
        }

        // 更新区域状态
        auto& state = region_states[region.id];
        state.has_detection_in_current_frame = has_detection_in_region;
        
        if (has_detection_in_region) {
            // 有检测目标，重置无检测跟踪
            if (state.is_tracking_no_detection) {
                state.is_tracking_no_detection = false;
                if (config.verbose) {
                    cout << "[Thread-Safe] Region " << region.id << " stopped tracking no detection" << endl;
                }
            }

            // 增加连续计数
            state.continuous_count++;
            state.last_detection_time = current_time;
            state.last_confidence = max_confidence;

            if (config.verbose) {
                cout << "[Thread-Safe] Region " << region.id << " continuous count: "
                     << state.continuous_count << "/" << config.continuous_count
                     << " (confidence: " << max_confidence << ")" << endl;
            }

            // 检查是否达到报警阈值且不在冷却期
            if (state.continuous_count >= config.continuous_count && !state.is_in_cooldown) {
                // 找到该区域内置信度最高的目标
                detect_result_t* best_detection = nullptr;
                for (int i = 0; i < detections.count; i++) {
                    const detect_result_t& detection = detections.results[i];
                    if (detection.prop >= config.confidence_threshold &&
                        isDetectionInRegion(detection, region) &&
                        detection.prop == max_confidence) {
                        best_detection = const_cast<detect_result_t*>(&detection);
                        break;
                    }
                }

                // 创建带有检测框的报警图片
                cv::Mat alarm_image = image.clone();
                if (best_detection != nullptr) {
                    drawDetectionOnImage(alarm_image, *best_detection);
                }

                // 发送报警（使用区域的device_id）
                // 使用当前加载的平面标定数据
                GroundCalibration* calibration_ptr = nullptr;
                if (calibration_manager && calibration_manager->isCalibrationValid()) {
                    // 使用配置中的实际标定数据
                    calibration_ptr = &config.ground_calibration;
                }

                bool alarm_sent = sendAlarmRequest(region.id, region.name, region.device_id, max_confidence, alarm_image, best_detection, calibration_ptr);
                
                // 无论接口调用成功还是失败都记录报警触发日志
                LOG_WARN_F("报警触发 - 区域[%s](%s) 置信度: %.2f%%", 
                          region.id.c_str(), region.name.c_str(), max_confidence * 100);
                
                if (alarm_sent) {
                    alarm_regions.push_back(region.id);
                    logAlarm(region.name, max_confidence);
                    LOG_INFO_F("报警接口调用成功 - 区域[%s]", region.id.c_str());
                } else {
                    LOG_ERROR_F("报警接口调用失败 - 区域[%s]", region.id.c_str());
                }

                // 设置冷却期并重置计数
                state.is_in_cooldown = true;
                state.last_alarm_time = current_time;
                state.continuous_count = 0;
                
                LOG_INFO_F("区域[%s]进入冷却期", region.id.c_str());

                if (config.verbose) {
                    cout << "[Thread-Safe] ALARM triggered for region " << region.id
                         << " (" << region.name << ") with confidence " << max_confidence << endl;
                }
            }
        } else {
            // 没有检测目标，开始或继续跟踪无检测状态
            if (!state.is_tracking_no_detection) {
                state.is_tracking_no_detection = true;
                state.no_detection_start_time = current_time;
                if (config.verbose) {
                    cout << "[Thread-Safe] Region " << region.id << " started tracking no detection" << endl;
                }
            }

            // 重置连续计数（连续检测中断需要重新计数）
            if (state.continuous_count > 0) {
                if (config.verbose) {
                    cout << "[Thread-Safe] Region " << region.id << " continuous count reset (was "
                         << state.continuous_count << ")" << endl;
                }
                state.continuous_count = 0;
            }
        }
        
        // 检查冷却期是否结束（使用渐进式冷却）
        if (state.is_in_cooldown) {
            auto cooldown_duration = chrono::duration_cast<chrono::minutes>(current_time - state.last_alarm_time);
            int effective_cooldown = calculateEffectiveCooldown(state);

            if (cooldown_duration.count() >= effective_cooldown) {
                state.is_in_cooldown = false;
                LOG_INFO_F("区域[%s]冷却期结束 (有效冷却时间: %d 分钟)", 
                          region.id.c_str(), effective_cooldown);
                if (config.verbose) {
                    cout << "[Thread-Safe] Region " << region.id << " cooldown period ended (effective: "
                         << effective_cooldown << " minutes)" << endl;
                }
            }
        }
    }
    
    return alarm_regions;
}

/**
 * @Description: 检查区域是否在冷却期（线程安全）
 */
bool GlobalAlarmManager::isRegionInCooldown(const string& region_id) {
    lock_guard<mutex> lock(states_mutex);

    auto it = region_states.find(region_id);
    if (it == region_states.end()) {
        return false;
    }

    auto& state = it->second;
    if (!state.is_in_cooldown) {
        return false;
    }

    // 检查冷却期是否已过（使用渐进式冷却）
    auto current_time = chrono::steady_clock::now();
    auto cooldown_duration = chrono::duration_cast<chrono::minutes>(current_time - state.last_alarm_time);
    int effective_cooldown = calculateEffectiveCooldown(state);

    if (cooldown_duration.count() >= effective_cooldown) {
        state.is_in_cooldown = false;
        return false;
    }

    return true;
}

/**
 * @Description: 设置区域冷却期（线程安全）
 */
void GlobalAlarmManager::setRegionCooldown(const string& region_id) {
    lock_guard<mutex> lock(states_mutex);
    
    auto it = region_states.find(region_id);
    if (it != region_states.end()) {
        it->second.is_in_cooldown = true;
        it->second.last_alarm_time = chrono::steady_clock::now();
    }
}

/**
 * @Description: 重置区域连续计数（线程安全）
 */
void GlobalAlarmManager::resetRegionCount(const string& region_id) {
    lock_guard<mutex> lock(states_mutex);
    
    auto it = region_states.find(region_id);
    if (it != region_states.end()) {
        it->second.continuous_count = 0;
    }
}

/**
 * @Description: 打印所有区域状态（调试用）
 */
void GlobalAlarmManager::printRegionStates() {
    lock_guard<mutex> lock(states_mutex);
    
    cout << "\n=== 全局报警状态 ===" << endl;
    for (const auto& pair : region_states) {
        const string& region_id = pair.first;
        const GlobalAlarmState& state = pair.second;
        
        cout << "区域 " << region_id << ": "
             << "连续计数=" << state.continuous_count 
             << ", 冷却中=" << (state.is_in_cooldown ? "是" : "否")
             << ", 最后置信度=" << state.last_confidence << endl;
    }
    cout << endl;
}

/**
 * @Description: 点在多边形内判断算法（射线投射法）
 */
bool GlobalAlarmManager::isPointInPolygon(int x, int y, const vector<pair<int, int>>& polygon) {
    int n = polygon.size();
    if (n < 3) return false;

    bool inside = false;
    int j = n - 1;

    for (int i = 0; i < n; i++) {
        int xi = polygon[i].first, yi = polygon[i].second;
        int xj = polygon[j].first, yj = polygon[j].second;

        if (((yi > y) != (yj > y)) && (x < (xj - xi) * (y - yi) / (yj - yi) + xi)) {
            inside = !inside;
        }
        j = i;
    }

    return inside;
}

/**
 * @Description: 获取检测框底线的中心点
 */
pair<int, int> GlobalAlarmManager::getBoxBottomCenter(const BOX_RECT& box) {
    int center_x = (box.left + box.right) / 2;  // 水平中心点
    int bottom_y = box.bottom;                   // 底线Y坐标
    return make_pair(center_x, bottom_y);
}

/**
 * @Description: 检查检测结果是否在指定区域内
 */
bool GlobalAlarmManager::isDetectionInRegion(const detect_result_t& detection, const DetectionRegion& region) {
    // 检查置信度是否满足阈值
    if (detection.prop < config.confidence_threshold) {
        return false;
    }

    // 获取检测框底线中心点
    pair<int, int> bottom_center = getBoxBottomCenter(detection.box);

    // 判断底线中心点是否在区域内
    return isPointInPolygon(bottom_center.first, bottom_center.second, region.points);
}


/**
 * @Description: 计算目标位置信息
 */
TargetPositionInfo GlobalAlarmManager::calculateTargetPosition(const detect_result_t& detection,
                                                              const GroundCalibration& calibration) {
    if (calibration_manager && calibration_manager->isCalibrationValid()) {
        return calibration_manager->calculateTargetPosition(detection.box, detection.name, detection.prop);
    }

    // 如果没有有效的标定，返回默认值
    TargetPositionInfo info;
    info.object_type = detection.name;
    info.confidence = detection.prop;
    info.width_ratio = 0.0f;
    info.calibration_valid = false;

    return info;
}

/**
 * @Description: 发送HTTP报警请求
 */
bool GlobalAlarmManager::sendAlarmRequest(const string& region_id, const string& region_name,
                                        const string& device_id, float confidence, const cv::Mat& alarm_image,
                                        const detect_result_t* detection, const GroundCalibration* calibration) {
    if (config.alarm_url.empty()) {
        LOG_WARN("报警URL未配置，跳过报警请求");
        if (config.verbose) {
            cout << "Warning: alarm_url is empty, skipping alarm request" << endl;
        }
        return false;
    }

    CURL *curl;
    CURLcode res;
    string response_string;
    bool success = false;

    curl = curl_easy_init();
    if (curl) {
        try {
            // 编码图片为base64
            string image_base64 = encodeImageToBase64(alarm_image);

            // 构建JSON请求体（使用区域的device_id）
            stringstream json_body;

            // 计算目标位置信息（如果有检测结果和标定数据）
            TargetPositionInfo position_info;
            bool has_position_info = false;

            if (config.verbose) {
                cout << "[Debug] sendAlarmRequest - detection: " << (detection ? "yes" : "no")
                     << ", calibration: " << (calibration ? "yes" : "no") << endl;
                if (calibration) {
                    cout << "[Debug] calibration enabled: " << calibration->enabled
                         << ", rectangles: " << calibration->rectangles.size() << endl;
                }
            }

            if (detection != nullptr && calibration != nullptr) {
                position_info = calculateTargetPosition(*detection, *calibration);
                has_position_info = position_info.calibration_valid;

                if (config.verbose) {
                    cout << "[Debug] position_info - valid: " << has_position_info
                         << ", width_ratio: " << position_info.width_ratio << endl;
                }
            }

            json_body << "{"
                     << "\"deviceId\":\"" << device_id << "\","
                     << "\"modelId\":\"big_slag_detection\","
                     << "\"alarmType\":\"1\","
                     << "\"alarmTime\":\"" << getCurrentTimeString() << "\","
                     << "\"alarmName\":\"检测到大渣\","
                     << "\"alarmPic\":\"" << image_base64 << "\","
                     << "\"squeezeMode\":\"1\","
                     << "\"remark\":\"检测置信度: " << fixed << setprecision(2) << confidence
                     << ", 区域: " << region_name;

            // 如果有位置信息，添加到remark中
            if (has_position_info) {
                json_body << ", 占据宽度: " << fixed << setprecision(1) << position_info.width_ratio * 100 << "%";
            }

            json_body << "\","
                     << "\"targetInfo\":{"
                     << "\"objectType\":\"" << (detection ? detection->name : "unknown") << "\","
                     << "\"confidence\":" << fixed << setprecision(3) << confidence;

            // 如果有位置信息，添加详细数据
            if (has_position_info) {
                json_body << ","
                         << "\"widthRatio\":" << fixed << setprecision(3) << position_info.width_ratio;
            }

            json_body << "}"
                     << "}";

            if (config.verbose) {
                cout << "[Global] Using device_id: " << device_id << " for alarm request" << endl;
            }

            string json_string = json_body.str();

            // 设置URL
            curl_easy_setopt(curl, CURLOPT_URL, config.alarm_url.c_str());

            // 设置POST请求
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, json_string.c_str());

            // 设置请求头
            struct curl_slist *headers = nullptr;
            headers = curl_slist_append(headers, "Content-Type: application/json");

            // 解析并添加自定义请求头
            if (!config.alarm_header.empty() && config.alarm_header != "{}") {
                if (config.verbose) {
                    cout << "[Global] Adding custom alarm headers: " << config.alarm_header << endl;
                }

                // 简单解析JSON格式的请求头并添加到curl
                // 这里假设alarm_header是简单的JSON格式，如 {"key1":"value1","key2":"value2"}
                // 实际项目中可能需要更完善的JSON解析
                string header_str = config.alarm_header;

                // 移除大括号
                if (header_str.front() == '{') header_str.erase(0, 1);
                if (header_str.back() == '}') header_str.pop_back();

                // 简单分割处理（假设没有复杂的嵌套）
                stringstream ss(header_str);
                string item;
                while (getline(ss, item, ',')) {
                    // 查找冒号分隔键值对
                    size_t colon_pos = item.find(':');
                    if (colon_pos != string::npos) {
                        string key = item.substr(0, colon_pos);
                        string value = item.substr(colon_pos + 1);

                        // 移除引号和空格
                        key.erase(remove(key.begin(), key.end(), '"'), key.end());
                        key.erase(remove(key.begin(), key.end(), ' '), key.end());
                        value.erase(remove(value.begin(), value.end(), '"'), value.end());
                        value.erase(remove(value.begin(), value.end(), ' '), value.end());

                        if (!key.empty() && !value.empty()) {
                            string header_line = key + ": " + value;
                            headers = curl_slist_append(headers, header_line.c_str());

                            if (config.verbose) {
                                cout << "[Global] Added header: " << header_line << endl;
                            }
                        }
                    }
                }
            }

            curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

            // 设置响应回调
            curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
            curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response_string);

            // 设置超时
            curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);

            // 执行请求
            res = curl_easy_perform(curl);

            if (res == CURLE_OK) {
                long response_code;
                curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);

                if (response_code == 200) {
                    success = true;
                    LOG_DEBUG_F("报警接口响应成功 - 区域[%s] HTTP状态码: %ld", region_id.c_str(), response_code);
                    if (config.verbose) {
                        cout << "[Global] Alarm request sent successfully for region " << region_id
                             << ", response: " << response_string << endl;
                    }
                } else {
                    LOG_ERROR_F("报警接口响应失败 - 区域[%s] HTTP状态码: %ld, 响应: %s", 
                               region_id.c_str(), response_code, response_string.c_str());
                    if (config.verbose) {
                        cout << "[Global] Alarm request failed with HTTP code: " << response_code
                             << ", response: " << response_string << endl;
                    }
                }
            } else {
                LOG_ERROR_F("报警接口网络请求失败 - 区域[%s] 错误: %s", region_id.c_str(), curl_easy_strerror(res));
                if (config.verbose) {
                    cout << "[Global] Alarm request failed: " << curl_easy_strerror(res) << endl;
                }
            }

            // 清理
            curl_slist_free_all(headers);

        } catch (const exception& e) {
            LOG_ERROR_F("报警接口调用异常 - 区域[%s] 异常: %s", region_id.c_str(), e.what());
            if (config.verbose) {
                cout << "[Global] Exception in sendAlarmRequest: " << e.what() << endl;
            }
        }

        curl_easy_cleanup(curl);
    }

    return success;
}

/**
 * @Description: 将图片编码为base64字符串（带MIME类型前缀）
 */
string GlobalAlarmManager::encodeImageToBase64(const cv::Mat& image) {
    vector<uchar> buffer;
    vector<int> params = {cv::IMWRITE_JPEG_QUALITY, 80}; // 设置JPEG质量为80%

    if (!cv::imencode(".jpg", image, buffer, params)) {
        if (config.verbose) {
            cout << "[Global] Failed to encode image to JPEG" << endl;
        }
        return "";
    }

    // 简单的base64编码实现
    const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    string base64_data;
    int val = 0, valb = -6;

    for (uchar c : buffer) {
        val = (val << 8) + c;
        valb += 8;
        while (valb >= 0) {
            base64_data.push_back(chars[(val >> valb) & 0x3F]);
            valb -= 6;
        }
    }

    if (valb > -6) {
        base64_data.push_back(chars[((val << 8) >> (valb + 8)) & 0x3F]);
    }

    while (base64_data.size() % 4) {
        base64_data.push_back('=');
    }

    // 添加MIME类型前缀
    string result = "data:image/jpeg;base64," + base64_data;

    if (config.verbose) {
        cout << "[Global] Image encoded to base64 with MIME prefix, size: " << result.length() << " chars" << endl;
    }

    return result;
}

/**
 * @Description: 获取当前时间字符串
 */
string GlobalAlarmManager::getCurrentTimeString() {
    auto now = chrono::system_clock::now();
    auto time_t = chrono::system_clock::to_time_t(now);
    auto tm = *localtime(&time_t);

    stringstream ss;
    ss << put_time(&tm, "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

/**
 * @Description: 记录报警日志
 */
void GlobalAlarmManager::logAlarm(const string& region_name, float confidence) {
    string timestamp = getCurrentTimeString();
    cout << "[" << timestamp << "] [Global] 区域[" << region_name << "] 检测到大渣，置信度: "
         << fixed << setprecision(2) << confidence << endl;
}

/**
 * @Description: 计算有效冷却期（考虑渐进式冷却）
 */
int GlobalAlarmManager::calculateEffectiveCooldown(const GlobalAlarmState& state) {
    // 如果未启用渐进式冷却或没有在跟踪无检测状态，使用基础冷却期
    if (!config.progressive_cooldown.enabled || !state.is_tracking_no_detection) {
        return config.cooldown_minutes;
    }

    // 获取无检测持续时间（支持小数分钟）
    float no_detection_duration = getNoDetectionDuration(state);
    float reset_time = config.progressive_cooldown.reset_cooldown_minutes;

    // 如果无检测时间超过重置时间，完全重置冷却期
    if (no_detection_duration >= reset_time) {
        if (config.verbose) {
            cout << "[Global] Progressive cooldown: no detection for " << fixed << setprecision(1)
                 << no_detection_duration << " minutes (>= " << reset_time
                 << "), cooldown completely reset" << endl;
        }
        return 0;  // 完全重置
    }

    // 线性减少冷却期：从100%减少到0%
    // 减少比例 = 无检测时间 / 重置时间
    double reduction_factor = no_detection_duration / reset_time;
    int effective_cooldown = static_cast<int>(config.cooldown_minutes * (1.0 - reduction_factor));

    if (config.verbose) {
        cout << "[Global] Progressive cooldown: no detection for " << fixed << setprecision(1)
             << no_detection_duration << " minutes, reduction " << fixed << setprecision(1)
             << (reduction_factor * 100) << "%, effective cooldown: " << effective_cooldown
             << " minutes" << endl;
    }

    return max(0, effective_cooldown);  // 确保不为负数
}

/**
 * @Description: 获取无检测持续时间（支持小数分钟）
 */
float GlobalAlarmManager::getNoDetectionDuration(const GlobalAlarmState& state) {
    if (!state.is_tracking_no_detection) {
        return 0.0f;
    }

    auto now = chrono::steady_clock::now();
    auto duration = chrono::duration_cast<chrono::milliseconds>(now - state.no_detection_start_time);
    // 转换为分钟（支持小数）
    return static_cast<float>(duration.count()) / 60000.0f;  // 毫秒转分钟
}

/**
 * @Description: 在图片上绘制检测框
 */
void GlobalAlarmManager::drawDetectionOnImage(cv::Mat& image, const detect_result_t& detection) {
    // 获取检测框坐标
    int x1 = detection.box.left;
    int y1 = detection.box.top;
    int x2 = detection.box.right;
    int y2 = detection.box.bottom;

    // 绘制红色检测框
    cv::rectangle(image, cv::Point(x1, y1), cv::Point(x2, y2), cv::Scalar(0, 0, 255), 3);

    // 创建置信度文字标签
    stringstream ss;
    ss << "conf:" << fixed << setprecision(2) << (detection.prop * 100) << "%";
    string text = ss.str();

    // 绘制白色文字
    cv::putText(image, text, cv::Point(x1, y1 + 20), cv::FONT_HERSHEY_SIMPLEX, 0.6, cv::Scalar(255, 255, 255), 2);

    if (config.verbose) {
        cout << "[Global] Drew detection box on alarm image: " << text
             << " at (" << x1 << "," << y1 << "," << x2 << "," << y2 << ")" << endl;
    }
}
