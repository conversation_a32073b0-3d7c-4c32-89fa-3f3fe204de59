/*
 * @Author: <PERSON> RF
 * @Date: 2025-03-16 18:13:52
 * @LastEditors: Li RF
 * @LastEditTime: 2025-03-27 16:31:11
 * @Description:
 * Email: <EMAIL>
 * Copyright (c) 2025 Li RF, All Rights Reserved.
 */

#include "FFmpegReader.hpp"

/**
 * @Description: 构造 FFmpeg 引擎
 * @return {*}
 */
FFmpegReader::FFmpegReader(const string& decodec, const int& accels_2d){
    // 获取 FFmpeg 版本信息
    const char* version = av_version_info();
    // 打印版本信息
    std::cout << "FFmpeg version: " << version << std::endl;
    
    this->decodec = decodec;
    this->accels_2d = accels_2d;
}

/**
 * @Description: 析构 FFmpeg 引擎
 * @return {*}
 */
FFmpegReader::~FFmpegReader() {
    if (tempFrame) 
        av_frame_free(&tempFrame);
    if (packet) 
        av_packet_free(&packet);
    if (codecContext) 
        avcodec_free_context(&codecContext);
    if (formatContext) 
        avformat_close_input(&formatContext);
        // 内部会调用 avformat_free_context(formatContext);
        
}

/**
 * @Description: 打开视频文件
 * @param {string} &filePath: 
 * @return {*}
 */
void FFmpegReader::openVideo(const std::string& filePath) {

    /* 分配一个 AVFormatContext */
    formatContext = avformat_alloc_context();
    if (!formatContext)
        throw std::runtime_error("Couldn't allocate format context");

    /* 设置输入选项，特别是对RTSP流的优化 */
    AVDictionary* inputOptions = nullptr;

    // 检查是否为RTSP流
    if (filePath.find("rtsp://") == 0) {
        std::cout << "Detected RTSP stream, applying RTSP-specific options..." << std::endl;

        // RTSP特定选项
        av_dict_set(&inputOptions, "rtsp_transport", "tcp", 0);  // 使用TCP传输（更稳定）
        av_dict_set(&inputOptions, "stimeout", "5000000", 0);   // 连接超时5秒
        av_dict_set(&inputOptions, "max_delay", "500000", 0);   // 最大延迟0.5秒
        av_dict_set(&inputOptions, "buffer_size", "1024000", 0); // 缓冲区大小1MB
        av_dict_set(&inputOptions, "fflags", "nobuffer", 0);    // 减少缓冲延迟
    }

    /* 打开视频文件或流 */
    // 并读取头部信息，此时编解码器尚未开启
    if (avformat_open_input(&formatContext, filePath.c_str(), nullptr, &inputOptions) != 0) {
        av_dict_free(&inputOptions);
        throw std::runtime_error("Couldn't open video file or stream: " + filePath);
    }

    // 释放选项字典
    av_dict_free(&inputOptions);

    /* 读取媒体文件的数据包以获取流信息 */
    if (avformat_find_stream_info(formatContext, nullptr) < 0)
        throw std::runtime_error("Couldn't find stream information");

    /* 查找视频流 AVMEDIA_TYPE_VIDEO */
    // -1, -1，意味着没有额外的选择条件，返回值是流索引
    videoStreamIndex = av_find_best_stream(formatContext, AVMEDIA_TYPE_VIDEO, -1, -1, nullptr, 0);
    if (videoStreamIndex < 0)
        throw std::runtime_error("Couldn't find a video stream");

    /* 查找解码器 */
    codec = avcodec_find_decoder_by_name(this->decodec.c_str());
    if (!codec)
        throw std::runtime_error("Decoder not found");
    
    /* 初始化编解码器上下文 */ 
    codecContext = avcodec_alloc_context3(codec);
    if (!codecContext)
        throw std::runtime_error("Couldn't allocate decoder context");

    /* 获取视频流，它包含了视频流的元数据和参数 */
    video_stream = formatContext->streams[videoStreamIndex];
    
    /* 复制视频参数到解码器上下文 */ 
    if (avcodec_parameters_to_context(codecContext, video_stream->codecpar) < 0)
        throw std::runtime_error("Couldn't copy decoder context");

    /* 自动选择线程数 */
    codecContext->thread_count = 0;

    /* 配置 MPP 解码器选项 */
    AVDictionary *opts = nullptr;
    if (this->decodec == "h264_rkmpp" || this->decodec == "hevc_rkmpp") {
        // 设置缓冲模式，这对 MPP 解码器很重要
        av_dict_set_int(&opts, "buf_mode", 1, 0);
        // 强制设置输出像素格式为 NV12
        av_dict_set(&opts, "output_format", "nv12", 0);
        std::cout << "Setting MPP decoder options: buf_mode=1, output_format=nv12" << std::endl;
    }

    /* 打开编解码器 */
    if (avcodec_open2(codecContext, codec, &opts) < 0) {
        if (opts) av_dict_free(&opts);
        throw std::runtime_error("Couldn't open decoder");
    }

    /* 释放选项字典 */
    if (opts) av_dict_free(&opts);
    
    /* 分配 AVPacket 和 AVFrame */ 
    tempFrame = av_frame_alloc();
    packet = av_packet_alloc();
    if (!tempFrame || !packet)
        throw std::runtime_error("Couldn't allocate frame or packet"); 
}

/**
 * @Description: 读取一帧
 * @param {Mat&} frame: 取出的帧
 * @return {*}
 */
bool FFmpegReader::readFrame(cv::Mat& frame) {
    // 持续尝试解码直到获得一个完整的帧
    while (true) {
        // 首先尝试从解码器获取已缓冲的帧
        int receive_ret = avcodec_receive_frame(codecContext, tempFrame);
        if (receive_ret == 0) {
            // 成功获得帧，进行格式转换
            goto convert_frame;
        } else if (receive_ret != AVERROR(EAGAIN)) {
            // 除了EAGAIN之外的其他错误
            if (receive_ret == AVERROR_EOF) {
                return false; // 解码结束
            }
            char errbuf[AV_ERROR_MAX_STRING_SIZE];
            av_strerror(receive_ret, errbuf, sizeof(errbuf));
            std::cerr << "Error receiving frame: " << errbuf << std::endl;
            return false;
        }

        // 需要更多数据，读取下一个包
        int read_ret = av_read_frame(formatContext, packet);
        if (read_ret < 0) {
            if (read_ret == AVERROR_EOF) {
                // 文件结束，刷新解码器
                avcodec_send_packet(codecContext, nullptr);
                continue; // 尝试获取剩余帧
            }
            return false; // 读取错误
        }

        // 检查是否为视频流
        if (packet->stream_index != videoStreamIndex) {
            av_packet_unref(packet);
            continue; // 跳过非视频包
        }

        // 发送数据包到解码器
        int send_ret = avcodec_send_packet(codecContext, packet);
        av_packet_unref(packet); // 立即释放包

        if (send_ret < 0) {
            if (send_ret == AVERROR(EAGAIN)) {
                // 解码器缓冲区满，先尝试接收帧
                continue;
            }
            char errbuf[AV_ERROR_MAX_STRING_SIZE];
            av_strerror(send_ret, errbuf, sizeof(errbuf));
            std::cerr << "Failed to send packet to decoder: " << errbuf << std::endl;
            return false;
        }

        // 发送成功后继续循环尝试接收帧
    }

convert_frame:

    // 成功读取一帧，保存在 tempFrame 中
    // 添加帧信息调试输出（仅前几帧）
    static int frame_count = 0;
    if (frame_count < 5) {
        std::cout << "Received frame " << frame_count << ": " << tempFrame->width << "x" << tempFrame->height
                  << ", format=" << tempFrame->format << " (expected NV12=" << AV_PIX_FMT_NV12 << ")" << std::endl;
        frame_count++;
    }

    // 将帧数据转换为 cv::Mat BGR 格式
    if (this->NV12_to_BGR(frame) != 0) {
        std::cerr << "Failed to convert YUV420SP to BGR" << std::endl;
        return false;
    }

    return true; // 处理完成
}

/**
 * @Description: 读取一帧并直接返回NV12格式数据（优化性能）
 * @param {cv::Mat&} nv12_frame: 输出的NV12格式图像
 * @return {bool} 是否成功读取
 */
bool FFmpegReader::readFrameNV12(cv::Mat& nv12_frame) {
    // 持续尝试解码直到获得一个完整的帧
    while (true) {
        // 首先尝试从解码器获取已缓冲的帧
        int receive_ret = avcodec_receive_frame(codecContext, tempFrame);
        if (receive_ret == 0) {
            // 成功获得帧，进行格式转换
            goto convert_frame_nv12;
        } else if (receive_ret != AVERROR(EAGAIN)) {
            // 除了EAGAIN之外的其他错误
            if (receive_ret == AVERROR_EOF) {
                return false; // 解码结束
            }
            char errbuf[AV_ERROR_MAX_STRING_SIZE];
            av_strerror(receive_ret, errbuf, sizeof(errbuf));
            std::cerr << "Error receiving frame: " << errbuf << std::endl;
            return false;
        }

        // 需要更多数据，读取下一个包
        int read_ret = av_read_frame(formatContext, packet);
        if (read_ret < 0) {
            if (read_ret == AVERROR_EOF) {
                // 文件结束，刷新解码器
                avcodec_send_packet(codecContext, nullptr);
                continue; // 尝试获取剩余帧
            }
            return false; // 读取错误
        }

        // 检查是否为视频流
        if (packet->stream_index != videoStreamIndex) {
            av_packet_unref(packet);
            continue; // 跳过非视频包
        }

        // 发送数据包到解码器
        int send_ret = avcodec_send_packet(codecContext, packet);
        av_packet_unref(packet); // 立即释放包

        if (send_ret < 0) {
            if (send_ret == AVERROR(EAGAIN)) {
                // 解码器缓冲区满，先尝试接收帧
                continue;
            }
            char errbuf[AV_ERROR_MAX_STRING_SIZE];
            av_strerror(send_ret, errbuf, sizeof(errbuf));
            std::cerr << "Failed to send packet to decoder: " << errbuf << std::endl;
            return false;
        }

        // 发送成功后继续循环尝试接收帧
    }

convert_frame_nv12:
    // 检查格式
    if (tempFrame->format != AV_PIX_FMT_NV12) {
        // 对于 MPP 解码器，format=0 可能仍然是有效的 NV12 数据
        if (tempFrame->format != AV_PIX_FMT_NONE && tempFrame->format != 0) {
            std::cerr << "Unexpected pixel format for NV12: " << tempFrame->format << std::endl;
            return false;
        }
    }

    // 创建NV12格式的Mat（Y + UV交错）
    nv12_frame.create(tempFrame->height + tempFrame->height / 2, tempFrame->width, CV_8UC1);

    // 将 AVFrame 内的数据转换为 OpenCV Mat 格式
    this->AV_Frame_To_CVMat(nv12_frame);

    return true;
}

/**
 * @Description: 转换格式，NV12 转 BGR
 *               该函数内有三种转换方式：
 *                  1. FFmpeg SwsContext 软件转换  
 *                  2. OpenCV 软件转换，可启用 opencl（目前区别不大）
 *                  3. RGA 硬件加速转换
 * @param {Mat&} frame: 
 * @return {*}
 */
int FFmpegReader::NV12_to_BGR(cv::Mat& bgr_frame) {
    // 增强格式检查和调试信息
    if (tempFrame->format != AV_PIX_FMT_NV12) {
        std::cerr << "Unexpected pixel format: " << tempFrame->format
                  << " (expected NV12=" << AV_PIX_FMT_NV12 << ")" << std::endl;

        // 对于 MPP 解码器，format=0 可能仍然是有效的 NV12 数据
        if (tempFrame->format == AV_PIX_FMT_NONE || tempFrame->format == 0) {
            if (this->decodec == "h264_rkmpp" || this->decodec == "hevc_rkmpp") {
                std::cout << "MPP decoder output format=0, treating as NV12" << std::endl;
                // 继续处理，假设数据是 NV12 格式
            } else {
                std::cerr << "Frame format is NONE or 0, frame may be invalid" << std::endl;
                return -EXIT_FAILURE;
            }
        } else {
            // 对于其他格式，暂时返回错误
            std::cerr << "Unsupported pixel format for conversion" << std::endl;
            return -EXIT_FAILURE;
        }
    }

    // 检查帧的有效性
    if (!tempFrame->data[0] || !tempFrame->data[1]) {
        std::cerr << "Frame data is null: Y=" << (void*)tempFrame->data[0]
                  << ", UV=" << (void*)tempFrame->data[1] << std::endl;
        return -EXIT_FAILURE;
    }

    // 检查帧尺寸
    if (tempFrame->width <= 0 || tempFrame->height <= 0) {
        std::cerr << "Invalid frame dimensions: " << tempFrame->width
                  << "x" << tempFrame->height << std::endl;
        return -EXIT_FAILURE;
    }

    // 添加调试信息，检查linesize
    static int debug_count = 0;
    if (debug_count < 3) {
        std::cout << "Frame " << debug_count << " linesize: Y=" << tempFrame->linesize[0]
                  << ", UV=" << tempFrame->linesize[1] << ", width=" << tempFrame->width
                  << ", height=" << tempFrame->height << std::endl;
        debug_count++;
    }

    // 设置输出帧的尺寸和格式，防止地址无法访问
    bgr_frame.create(tempFrame->height, tempFrame->width, CV_8UC3);

#if 0 
    // 方式1：使用 FFmpeg SwsContext 软件转换
    return this->FFmpeg_yuv420sp_to_bgr(bgr_frame);
#endif

    // 创建一个完整的 NV12 数据块（Y + UV 交错）
    cv::Mat nv12_mat(tempFrame->height + tempFrame->height / 2, tempFrame->width, CV_8UC1);
    // 将 AVFrame 内的数据，转换为 OpenCV Mat 格式保存
    this->AV_Frame_To_CVMat(nv12_mat);

    // 硬件加速
    if (this->accels_2d == ACCELS_2D::ACC_OPENCV) {
        // 方式2：使用 OpenCV 软件转换
        cv::cvtColor(nv12_mat, bgr_frame, cv::COLOR_YUV2BGR_NV12);
        return EXIT_SUCCESS;
    } else if (this->accels_2d == ACCELS_2D::ACC_RGA) {
        // 方式3：使用 RGA 硬件加速转换
        return RGA_yuv420sp_to_bgr((uint8_t *)nv12_mat.data, tempFrame->width, tempFrame->height, bgr_frame);
    }
    else
        return -EXIT_FAILURE;
}

/**
 * @Description: 转换格式，NV12 转 RGB（跳过BGR中间步骤，优化性能）
 * @param {Mat&} rgb_frame: 输出的RGB图像
 * @return {*}
 */
int FFmpegReader::NV12_to_RGB(cv::Mat& rgb_frame) {
    // 增强格式检查和调试信息
    if (tempFrame->format != AV_PIX_FMT_NV12) {
        std::cerr << "Unexpected pixel format: " << tempFrame->format
                  << " (expected NV12=" << AV_PIX_FMT_NV12 << ")" << std::endl;
        return -EXIT_FAILURE;
    }

    // 创建一个完整的 NV12 数据块（Y + UV 交错）
    cv::Mat nv12_mat(tempFrame->height + tempFrame->height / 2, tempFrame->width, CV_8UC1);
    // 将 AVFrame 内的数据，转换为 OpenCV Mat 格式保存
    this->AV_Frame_To_CVMat(nv12_mat);

    // 硬件加速直接转换为RGB
    if (this->accels_2d == ACCELS_2D::ACC_OPENCV) {
        // 方式1：使用 OpenCV 软件转换 NV12 → RGB
        cv::cvtColor(nv12_mat, rgb_frame, cv::COLOR_YUV2RGB_NV12);
        return EXIT_SUCCESS;
    } else if (this->accels_2d == ACCELS_2D::ACC_RGA) {
        // 方式2：使用 RGA 硬件加速转换 NV12 → RGB
        return RGA_yuv420sp_to_rgb((uint8_t *)nv12_mat.data, tempFrame->width, tempFrame->height, rgb_frame);
    }
    else
        return -EXIT_FAILURE;
}

/**
 * @Description: FFmpeg SwsContext 软件转换（耗时大约 10 ms）
 * @param {AVFrame} *yuv420Frame: 
 * @return {*}
 */
int FFmpegReader::FFmpeg_yuv420sp_to_bgr(cv::Mat& bgr_frame) {
    int srcW = tempFrame->width;
    int srcH = tempFrame->height;

    // 创建 SwsContext 用于颜色空间转换
    SwsContext *swsCtx = sws_getContext(srcW, srcH, AV_PIX_FMT_NV12,
        srcW, srcH, AV_PIX_FMT_BGR24, SWS_BICUBIC, NULL, NULL, NULL);
    if (!swsCtx) {
        std::cerr << "Could not initialize the conversion context" << std::endl;
        return -EXIT_FAILURE;
    }

    // 创建一个临时的 AVFrame 用于存储转换后的 BGR 数据
    AVFrame *bgr24Frame = av_frame_alloc();
    if (!bgr24Frame) {
        std::cerr << "Could not allocate the BGR frame" << std::endl;
        sws_freeContext(swsCtx);
        return -EXIT_FAILURE;
    }

    // 填充临时 AVFrame 的数据指针和行尺寸
    av_image_fill_arrays(bgr24Frame->data, bgr24Frame->linesize, bgr_frame.data, AV_PIX_FMT_BGR24, srcW, srcH, 1);
    // 进行颜色空间转换
    sws_scale(swsCtx, (const uint8_t* const*)tempFrame->data, tempFrame->linesize, 0, srcH, bgr24Frame->data, bgr24Frame->linesize);
    
    // 释放资源
    av_frame_free(&bgr24Frame);
    sws_freeContext(swsCtx);

    return EXIT_SUCCESS;
}

/**
 * @Description: 将 AVFrame 内的数据，转换为 OpenCV Mat 格式保存
 * @param {Mat&} frame: 
 * @return {*}
 */
void FFmpegReader::AV_Frame_To_CVMat(cv::Mat& nv12_mat) {
    // 对于MPP解码器，需要特殊处理数据布局
    if (this->decodec == "h264_rkmpp" || this->decodec == "hevc_rkmpp") {
        // MPP解码器的特殊处理

        // 拷贝 Y 分量 - 逐行拷贝以处理可能的内存对齐
        for (int y = 0; y < tempFrame->height; y++) {
            memcpy(nv12_mat.data + y * tempFrame->width,
                   tempFrame->data[0] + y * tempFrame->linesize[0],
                   tempFrame->width);
        }

        // 拷贝 UV 分量 - MPP解码器的UV数据布局特殊处理
        // MPP解码器的UV linesize可能是width/2而不是width，需要特殊处理
        uint8_t* uv_dst = nv12_mat.data + tempFrame->height * tempFrame->width;

        if (tempFrame->linesize[1] == tempFrame->width / 2) {
            // MPP解码器输出：UV分量可能是分离的U和V平面，需要特殊处理
            std::cout << "MPP decoder UV format: linesize=" << tempFrame->linesize[1]
                      << ", width=" << tempFrame->width << ", checking UV layout..." << std::endl;

            // 检查是否有第三个平面（V平面）
            if (tempFrame->data[2] != nullptr) {
                // 分离的U和V平面：data[1]=U平面, data[2]=V平面
                std::cout << "Separate U/V planes detected, interleaving..." << std::endl;
                for (int y = 0; y < tempFrame->height / 2; y++) {
                    uint8_t* src_u = tempFrame->data[1] + y * tempFrame->linesize[1];
                    uint8_t* src_v = tempFrame->data[2] + y * tempFrame->linesize[2];
                    uint8_t* dst_line = uv_dst + y * tempFrame->width;

                    // 交错存储U和V：UVUVUV...
                    for (int x = 0; x < tempFrame->width / 2; x++) {
                        dst_line[x * 2] = src_u[x];     // U
                        dst_line[x * 2 + 1] = src_v[x]; // V
                    }
                }
            } else {
                // 可能是半宽度的交错UV数据，直接拷贝并扩展
                std::cout << "Half-width UV data, expanding..." << std::endl;
                for (int y = 0; y < tempFrame->height / 2; y++) {
                    uint8_t* src_line = tempFrame->data[1] + y * tempFrame->linesize[1];
                    uint8_t* dst_line = uv_dst + y * tempFrame->width;

                    // 直接拷贝，假设已经是UVUV格式但宽度减半
                    for (int x = 0; x < tempFrame->width / 2; x++) {
                        dst_line[x * 2] = src_line[x * 2];     // U
                        dst_line[x * 2 + 1] = src_line[x * 2 + 1]; // V
                    }
                }
            }
        } else {
            // 标准NV12格式，直接拷贝
            for (int y = 0; y < tempFrame->height / 2; y++) {
                memcpy(uv_dst + y * tempFrame->width,
                       tempFrame->data[1] + y * tempFrame->linesize[1],
                       tempFrame->width);
            }
        }
    } else {
        // 标准处理方式

        // 拷贝 Y 分量（确保连续）
        cv::Mat y_plane(tempFrame->height, tempFrame->width, CV_8UC1, tempFrame->data[0], tempFrame->linesize[0]);
        y_plane.copyTo(nv12_mat(cv::Rect(0, 0, tempFrame->width, tempFrame->height)));

        // 无论FFmpeg的linesize[1]是否有填充字节，都能正确处理UV数据，兼容FFmpeg内存对齐

        // FFmpeg的 linesize[1] 可能有填充字节，导致直接拷贝UV数据时错位。通过 reshape 可以：
        // 先将双通道UV (CV_8UC2) 展开为单通道交错排列的字节流。
        // 再调整形状为 height/2 × width，确保与Y分量拼接后符合NV12标准布局。

        // 将UV分量视为CV_8UC2（双通道），明确表示U和V是交错的（NV12的标准布局）。通过reshape操作确保数据连续性
        cv::Mat uv_plane(tempFrame->height / 2, tempFrame->width / 2, CV_8UC2, tempFrame->data[1], tempFrame->linesize[1]);
        cv::Mat uv_interleaved;

        // 显式处理UV内存布局，确保符合OpenCV对COLOR_YUV2BGR_NV12的要求
        // 参数 1：保持单通道不变
        uv_plane.reshape(1, tempFrame->height / 2 * tempFrame->width / 2 * 2).copyTo(uv_interleaved); // 转换为交错排列
        uv_interleaved.reshape(1, tempFrame->height / 2).copyTo(nv12_mat(cv::Rect(0, tempFrame->height, tempFrame->width, tempFrame->height / 2)));
    }
}

/**
 * @Description: 关闭视频文件
 * @return {*}
 */
void FFmpegReader::closeVideo() {
    if (codecContext == nullptr || tempFrame == nullptr) {
        return;
    }
    // 刷新解码器，确保所有帧都被解码
    avcodec_send_packet(codecContext, nullptr);
    // 处理剩余的帧
    while (avcodec_receive_frame(codecContext, tempFrame) == 0) {
        std::cout << "Flushed frame with PTS: " << tempFrame->pts << std::endl;
    }
}

/**
 * @Description: 打印视频信息
 * @return {*}
 */
void FFmpegReader::print_video_info(const string& filePath) {
    std::cout << "==== File Format Information ====" << std::endl;
    av_dump_format(formatContext, 0, filePath.c_str(), 0);
}

/**
 * @Description: 获取视频的分辨率
 * @return {*}
 */
int FFmpegReader::getWidth() const {
    return codecContext->width;
}

/**
 * @Description: 获取视频高度
 * @return {*}
 */
int FFmpegReader::getHeight() const {
    return codecContext->height;
}

/**
 * @Description: 获取时间基
 * @return {*}
 */
AVRational FFmpegReader::getTimeBase() const {
    return formatContext->streams[videoStreamIndex]->time_base;
}

/**
 * @Description: 获取帧率
 * @return {*}
 */
double FFmpegReader::getFrameRate() const {
    AVRational frameRate = video_stream->avg_frame_rate;
    // 将 AVRational 转换为 double
    double fps = av_q2d(frameRate); 
    return fps;
}