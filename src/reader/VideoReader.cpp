/*
 * @Author: <PERSON> RF
 * @Date: 2025-03-15 14:26:22
 * @LastEditors: Li RF
 * @LastEditTime: 2025-03-21 13:30:29
 * @Description: 
 * Email: <EMAIL>
 * Copyright (c) 2025 Li RF, All Rights Reserved.
 */
#include <iostream>
#include "VideoReader.hpp"
#include "OpencvReader.hpp"
#include "FFmpegReader.hpp"
#include "LogManager.hpp"

/**
 * @Description: 构造函数，初始化视频加载引擎
 * @param {AppConfig&} config: 命令行参数
 * @return {*}
 */
VideoReader::VideoReader(const AppConfig& config) {
    // 保存配置信息用于重连
    input_source = config.input;
    decoder_name = config.decodec;
    read_engine_type = config.read_engine;
    accels_2d_type = config.accels_2d;

    int engine = config.read_engine;

    /* 如果输入源为摄像头，只使用 OpenCV，由于帧率限制不需要硬件加速 */
    if (config.input_format == INPUT_FORMAT::IN_CAMERA)
        engine = READ_ENGINE::EN_OPENCV;

    /* 加载引擎 */
    try {
        this->Init_Load_Engine(engine, config.decodec, config.accels_2d);
        LOG_INFO("视频引擎加载成功");
    } catch(const std::exception& e) {
        LOG_ERROR("视频引擎加载失败: " + std::string(e.what()));
        std::cerr << "加载引擎错误: " << e.what() << std::endl;
        throw e;
    }

    /* 打开视频文件 */
    try {
        reader_ptr->openVideo(config.input);
        LOG_INFO("视频流连接成功: " + config.input);
    } catch(const std::exception& e) {
        LOG_ERROR("视频流连接失败: " + config.input + " - " + std::string(e.what()));
        std::cerr << "打开视频文件错误: " << e.what() << std::endl;
        throw e;
    }
    
    

    
}

/**
 * @Description: 析构函数：释放资源
 * @return {*}
 */
VideoReader::~VideoReader() {
    this->Close_Video();
}

/**
 * @Description: 
 * @param {int&} engine: 初始化加载引擎
 * @param {string&} decodec: 解码器
 * @param {int&} accels_2d: 2d 硬件加速
 * @return {*}
 */
void VideoReader::Init_Load_Engine(const int& engine, const string& decodec, const int& accels_2d) {
    /* 加载引擎 */
    switch (engine)
    {
    case READ_ENGINE::EN_FFMPEG:
        reader_ptr = std::make_unique<FFmpegReader>(decodec, accels_2d);
        break;
    case READ_ENGINE::EN_OPENCV:
        reader_ptr = std::make_unique<OpencvReader>();
        break;
    default:
        throw std::invalid_argument("未知的 Reader 类型");
    }
}

/**
 * @Description: 读取一帧
 * @param {AVFrame} *frame: 取出的帧
 * @return {*}
 */
bool VideoReader::readFrame(cv::Mat& frame) {
    return reader_ptr->readFrame(frame);
}

/**
 * @Description: 读取一帧NV12格式数据
 * @param {cv::Mat&} nv12_frame: 输出的NV12格式图像
 * @return {bool} 是否成功读取
 */
bool VideoReader::readFrameNV12(cv::Mat& nv12_frame) {
    return reader_ptr->readFrameNV12(nv12_frame);
}

/**
 * @Description: 关闭视频文件并释放资源
 * @return {*}
 */
void VideoReader::Close_Video() {
    if (reader_ptr) {
        reader_ptr->closeVideo();
        LOG_INFO("视频流连接已断开");
    }
}

/**
 * @Description: 重新初始化视频连接
 * @param {string&} input: 输入源（可选，为空则使用原配置）
 * @param {string&} decodec: 解码器（可选，为空则使用原配置）
 * @return {int} 0=成功, -1=失败
 */
int VideoReader::Init_Video(const string& input, const string& decodec) {
    try {
        // 如果reader_ptr存在，先关闭当前连接
        if (reader_ptr) {
            reader_ptr->closeVideo();
        }

        // 使用传入的参数或原配置
        string target_input = input.empty() ? input_source : input;
        string target_decoder = decodec.empty() ? decoder_name : decodec;

        // 重新初始化引擎（以防需要）
        this->Init_Load_Engine(read_engine_type, target_decoder, accels_2d_type);

        // 重新打开视频文件
        reader_ptr->openVideo(target_input);
        LOG_INFO("视频流重连成功: " + target_input);
        return 0;
    } catch(const std::exception& e) {
        LOG_ERROR("视频流重连失败: " + (input.empty() ? input_source : input) + " - " + std::string(e.what()));
        return -1;
    }
}

