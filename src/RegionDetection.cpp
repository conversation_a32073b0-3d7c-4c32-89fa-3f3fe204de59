/*
 * @Author: Li RF
 * @Date: 2025-01-23 10:00:00
 * @LastEditors: Li RF
 * @LastEditTime: 2025-01-23 10:00:00
 * @Description: 区域检测算法实现
 * Email: <EMAIL>
 * Copyright (c) 2025 Li RF, All Rights Reserved.
 */
#include "RegionDetection.hpp"
#include "LogManager.hpp"
#include <iostream>
#include <algorithm>
#include <fstream>
#include <sstream>
#include <iomanip>
#include <ctime>
#include <curl/curl.h>
#include <opencv2/opencv.hpp>

using namespace std;

/**
 * @Description: 构造函数
 */
RegionDetectionManager::RegionDetectionManager(const AppConfig& app_config) : config(app_config) {
    // 初始化所有区域的状态
    for (const auto& region : config.regions) {
        region_states[region.id] = RegionDetectionState();
    }

#ifdef WITH_FREETYPE
    // 初始化中文字体
    font_loaded = initChineseFont();
    if (config.verbose) {
        cout << "Chinese font support: " << (font_loaded ? "enabled" : "disabled") << endl;
    }
#else
    if (config.verbose) {
        cout << "Chinese font support: not compiled (use -DWITH_FREETYPE=ON)" << endl;
    }
#endif

    if (config.verbose) {
        cout << "RegionDetectionManager initialized with " << config.regions.size() << " regions" << endl;
        cout << "Confidence threshold: " << config.confidence_threshold << endl;
        cout << "Continuous count threshold: " << config.continuous_count << endl;
        cout << "Cooldown minutes: " << config.cooldown_minutes << endl;
    }
}







/**
 * @Description: 处理检测结果，更新区域状态并判断是否需要报警
 */
vector<string> RegionDetectionManager::processDetections(const detect_result_group_t& detections, cv::Mat& image) {
    vector<string> alarm_regions;
    auto current_time = chrono::steady_clock::now();

    // 简化处理：不进行区域判断，直接检查是否有满足阈值的检测结果
    bool has_valid_detection = false;
    float max_confidence = 0.0f;

    for (int i = 0; i < detections.count; i++) {
        const detect_result_t& detection = detections.results[i];

        // 检查置信度是否达到阈值
        if (detection.prop >= config.confidence_threshold) {
            has_valid_detection = true;
            max_confidence = max(max_confidence, detection.prop);

            if (config.verbose) {
                cout << "Valid detection found: " << detection.name
                     << " (confidence=" << detection.prop << ")" << endl;
            }
        }
    }

    // 为所有区域应用相同的检测结果
    for (const auto& region : config.regions) {
        bool has_detection_in_region = has_valid_detection;
        float region_max_confidence = max_confidence;

        // 更新区域状态
        auto& state = region_states[region.id];

        if (has_detection_in_region) {
            // 有检测目标，增加连续计数
            state.continuous_count++;
            state.last_detection_time = current_time;
            state.last_confidence = region_max_confidence;

            if (config.verbose) {
                cout << "Region " << region.id << " continuous count: "
                     << state.continuous_count << "/" << config.continuous_count
                     << " (confidence: " << region_max_confidence << ")" << endl;
            }

            // 检查是否达到报警阈值且不在冷却期
            if (state.continuous_count >= config.continuous_count && !isRegionInCooldown(region.id)) {
                // 发送报警
                if (sendAlarmRequest(region.id, region.name, region_max_confidence, image)) {
                    alarm_regions.push_back(region.id);
                    logAlarm(region.name, region_max_confidence);
                }

                // 设置冷却期并重置计数
                setRegionCooldown(region.id);
                resetRegionCount(region.id);

                if (config.verbose) {
                    cout << "ALARM triggered for region " << region.id
                         << " (" << region.name << ") with confidence " << region_max_confidence << endl;
                }
            }
        } else {
            // 没有检测目标，重置连续计数（连续检测中断需要重新计数）
            if (state.continuous_count > 0) {
                if (config.verbose) {
                    cout << "Region " << region.id << " continuous count reset (was "
                         << state.continuous_count << ")" << endl;
                }
                resetRegionCount(region.id);
            }
        }
    }

    return alarm_regions;
}

/**
 * @Description: 检查区域是否在冷却期
 */
bool RegionDetectionManager::isRegionInCooldown(const string& region_id) {
    auto it = region_states.find(region_id);
    if (it == region_states.end()) {
        return false;
    }
    
    auto& state = it->second;
    if (!state.is_in_cooldown) {
        return false;
    }
    
    // 检查冷却时间是否已过
    auto now = chrono::steady_clock::now();
    auto cooldown_duration = chrono::minutes(config.cooldown_minutes);
    
    if (now - state.last_alarm_time >= cooldown_duration) {
        // 冷却期结束
        state.is_in_cooldown = false;
        if (config.verbose) {
            cout << "Region " << region_id << " cooldown period ended" << endl;
        }
        return false;
    }
    
    return true;
}

/**
 * @Description: 重置区域的连续检测计数
 */
void RegionDetectionManager::resetRegionCount(const string& region_id) {
    auto it = region_states.find(region_id);
    if (it != region_states.end()) {
        it->second.continuous_count = 0;
    }
}

/**
 * @Description: 设置区域进入冷却期
 */
void RegionDetectionManager::setRegionCooldown(const string& region_id) {
    auto it = region_states.find(region_id);
    if (it != region_states.end()) {
        auto& state = it->second;
        state.is_in_cooldown = true;
        state.last_alarm_time = chrono::steady_clock::now();
        
        if (config.verbose) {
            cout << "Region " << region_id << " entered cooldown period for " 
                 << config.cooldown_minutes << " minutes" << endl;
        }
    }
}

/**
 * @Description: 获取区域当前状态信息
 */
RegionDetectionState RegionDetectionManager::getRegionState(const string& region_id) {
    auto it = region_states.find(region_id);
    if (it != region_states.end()) {
        return it->second;
    }
    return RegionDetectionState();
}

/**
 * @Description: 打印所有区域状态
 */
void RegionDetectionManager::printRegionStates() {
    cout << "=== Region Detection States ===" << endl;
    for (const auto& pair : region_states) {
        const string& region_id = pair.first;
        const RegionDetectionState& state = pair.second;
        
        cout << "Region " << region_id << ": "
             << "count=" << state.continuous_count 
             << ", cooldown=" << (state.is_in_cooldown ? "yes" : "no");
        
        if (state.is_in_cooldown) {
            auto now = chrono::steady_clock::now();
            auto elapsed = chrono::duration_cast<chrono::minutes>(now - state.last_alarm_time);
            cout << " (elapsed=" << elapsed.count() << "min)";
        }
        cout << endl;
    }
    cout << "===============================" << endl;
}

/**
 * @Description: 在图像上绘制所有检测区域
 */
void RegionDetectionManager::drawRegions(cv::Mat& image) {
    if (config.regions.empty()) {
        return;
    }

    // 为不同区域使用不同颜色（优化后的颜色方案）
    vector<cv::Scalar> colors = {
        cv::Scalar(0, 255, 0),      // 绿色 - 区域1
        cv::Scalar(255, 0, 0),      // 蓝色 - 区域2
        cv::Scalar(255, 255, 0),    // 青色 - 区域3
        cv::Scalar(255, 0, 255),    // 洋红色 - 区域4
        cv::Scalar(0, 255, 255),    // 黄色 - 区域5
        cv::Scalar(255, 165, 0),    // 橙色 - 区域6
        cv::Scalar(128, 0, 255),    // 紫色 - 区域7
        cv::Scalar(0, 128, 255)     // 浅蓝色 - 区域8
    };

    for (size_t i = 0; i < config.regions.size(); ++i) {
        const auto& region = config.regions[i];
        cv::Scalar color = colors[i % colors.size()];

        // 绘制区域
        drawSingleRegion(image, region, color, 2);

        // 在区域附近添加文本标签
        if (!region.points.empty()) {
            cv::Point label_pos(region.points[0].first, region.points[0].second - 24);

            // 只显示区域名称，避免中文显示问题，使用区域ID作为标识
            string label_text = region.name;

            // 绘制文本 - 使用中文字体支持（无背景）
            putChineseText(image, label_text, label_pos, 16, color, 1);
        }
    }
}

/**
 * @Description: 在图像上绘制单个区域
 */
void RegionDetectionManager::drawSingleRegion(cv::Mat& image, const DetectionRegion& region,
                                            const cv::Scalar& color, int thickness) {
    if (region.points.size() < 3) {
        return; // 至少需要3个点构成多边形
    }

    // 转换坐标点格式
    vector<cv::Point> cv_points;
    for (const auto& point : region.points) {
        cv_points.push_back(cv::Point(point.first, point.second));
    }

    // 绘制多边形边框
    for (size_t i = 0; i < cv_points.size(); ++i) {
        cv::Point start = cv_points[i];
        cv::Point end = cv_points[(i + 1) % cv_points.size()]; // 连接到下一个点，最后一个点连接到第一个点
        cv::line(image, start, end, color, thickness);
    }

    // 可选：填充半透明区域
    if (thickness > 0) {
        cv::Mat overlay = image.clone();
        vector<vector<cv::Point>> contours = {cv_points};
        cv::fillPoly(overlay, contours, color);
        cv::addWeighted(image, 0.8, overlay, 0.2, 0, image); // 20%透明度
    }
}

/**
 * @Description: 初始化中文字体
 */
bool RegionDetectionManager::initChineseFont(const string& fontPath) {
#ifdef WITH_FREETYPE
    try {
        ft2 = cv::freetype::createFreeType2();

        string actualFontPath = fontPath;
        if (actualFontPath.empty()) {
            actualFontPath = getSystemChineseFontPath();
        }

        if (actualFontPath.empty()) {
            if (config.verbose) {
                cout << "Warning: No Chinese font found, using fallback" << endl;
            }
            return false;
        }

        ft2->loadFontData(actualFontPath, 0);

        if (config.verbose) {
            cout << "Chinese font loaded: " << actualFontPath << endl;
        }

        return true;
    } catch (const exception& e) {
        if (config.verbose) {
            cout << "Failed to load Chinese font: " << e.what() << endl;
        }
        return false;
    }
#else
    return false;
#endif
}

/**
 * @Description: 绘制中文文字
 */
void RegionDetectionManager::putChineseText(cv::Mat& image, const string& text, cv::Point position,
                                          int fontSize, const cv::Scalar& color, int thickness) {
#ifdef WITH_FREETYPE
    if (font_loaded && ft2) {
        try {
            ft2->putText(image, text, position, fontSize, color, thickness, cv::LINE_8, false);
            return;
        } catch (const exception& e) {
            if (config.verbose) {
                cout << "FreeType putText failed: " << e.what() << ", falling back to ASCII" << endl;
            }
        }
    }
#endif

    // 回退方案：使用ASCII替换或英文标签
    string fallbackText = text;

    // 中文到英文的映射
    map<string, string> chineseToEnglish = {
        {"区域", "Region"},
        {"检测到大渣", "Large Slag Detected"},
        {"报警", "ALARM"},
        {"冷却中", "Cooldown"},
        {"置信度", "Confidence"},
        {"连续检测", "Continuous Detection"}
    };

    // 尝试替换中文
    for (const auto& pair : chineseToEnglish) {
        size_t pos = fallbackText.find(pair.first);
        if (pos != string::npos) {
            fallbackText.replace(pos, pair.first.length(), pair.second);
        }
    }

    // 使用OpenCV默认字体绘制
    cv::putText(image, fallbackText, position, cv::FONT_HERSHEY_SIMPLEX,
               fontSize / 20.0, color, thickness);
}

/**
 * @Description: 获取系统中文字体路径
 */
string RegionDetectionManager::getSystemChineseFontPath() {
    vector<string> fontPaths = {
        // Ubuntu/Debian 常见中文字体
        "/usr/share/fonts/truetype/wqy/wqy-zenhei.ttc",
        "/usr/share/fonts/truetype/wqy/wqy-microhei.ttc",
        "/usr/share/fonts/truetype/arphic/ukai.ttc",
        "/usr/share/fonts/truetype/arphic/uming.ttc",
        "/usr/share/fonts/truetype/droid/DroidSansFallbackFull.ttf",

        // CentOS/RHEL 常见中文字体
        "/usr/share/fonts/wqy-zenhei/wqy-zenhei.ttc",
        "/usr/share/fonts/wqy-microhei/wqy-microhei.ttc",

        // 其他可能的路径
        "/System/Library/Fonts/PingFang.ttc",  // macOS
        "/usr/share/fonts/truetype/liberation/LiberationSans-Regular.ttf",

        // 项目本地字体（如果有的话）
        "./fonts/simhei.ttf",
        "./fonts/simsun.ttf"
    };

    for (const string& path : fontPaths) {
        ifstream file(path);
        if (file.good()) {
            file.close();
            return path;
        }
    }

    return ""; // 没有找到合适的字体
}

/**
 * @Description: HTTP响应回调函数
 */
static size_t WriteCallback(void *contents, size_t size, size_t nmemb, string *userp) {
    userp->append((char*)contents, size * nmemb);
    return size * nmemb;
}

/**
 * @Description: 发送HTTP报警请求
 */
bool RegionDetectionManager::sendAlarmRequest(const string& region_id, const string& region_name,
                                            float confidence, const cv::Mat& alarm_image) {
    if (config.alarm_url.empty()) {
        LOG_WARN("报警URL未配置，跳过报警请求");
        if (config.verbose) {
            cout << "Warning: alarm_url is empty, skipping alarm request" << endl;
        }
        return false;
    }

    CURL *curl;
    CURLcode res;
    string response_string;
    bool success = false;

    curl = curl_easy_init();
    if (curl) {
        try {
            // 编码图片为base64
            string image_base64 = encodeImageToBase64(alarm_image);

            // 构建JSON请求体
            stringstream json_body;
            json_body << "{"
                     << "\"deviceId\":\"" << config.device_id << "\","
                     << "\"modelId\":\"big_slag_detection\","
                     << "\"alarmType\":\"1\","
                     << "\"alarmTime\":\"" << getCurrentTimeString() << "\","
                     << "\"alarmName\":\"检测到大渣\","
                     << "\"alarmPic\":\"" << image_base64 << "\","
                     << "\"squeezeMode\":\"1\","
                     << "\"remark\":\"检测置信度: " << fixed << setprecision(2) << confidence
                     << ", 区域: " << region_name << "\""
                     << "}";

            string json_string = json_body.str();

            // 设置URL
            curl_easy_setopt(curl, CURLOPT_URL, config.alarm_url.c_str());

            // 设置POST请求
            curl_easy_setopt(curl, CURLOPT_POSTFIELDS, json_string.c_str());

            // 设置请求头
            struct curl_slist *headers = nullptr;
            headers = curl_slist_append(headers, "Content-Type: application/json");

            // 解析并添加自定义请求头
            if (!config.alarm_header.empty() && config.alarm_header != "{}") {
                // 这里简化处理，实际应该解析JSON格式的header
                // 暂时假设alarm_header格式正确
                if (config.verbose) {
                    cout << "Custom alarm headers: " << config.alarm_header << endl;
                }
            }

            curl_easy_setopt(curl, CURLOPT_HTTPHEADER, headers);

            // 设置响应回调
            curl_easy_setopt(curl, CURLOPT_WRITEFUNCTION, WriteCallback);
            curl_easy_setopt(curl, CURLOPT_WRITEDATA, &response_string);

            // 设置超时
            curl_easy_setopt(curl, CURLOPT_TIMEOUT, 10L);

            // 执行请求
            res = curl_easy_perform(curl);

            if (res == CURLE_OK) {
                long response_code;
                curl_easy_getinfo(curl, CURLINFO_RESPONSE_CODE, &response_code);

                if (response_code == 200) {
                    success = true;
                    LOG_DEBUG_F("报警接口响应成功 - 区域[%s] HTTP状态码: %ld", region_id.c_str(), response_code);
                    if (config.verbose) {
                        cout << "Alarm request sent successfully for region " << region_id
                             << ", response: " << response_string << endl;
                    }
                } else {
                    LOG_ERROR_F("报警接口响应失败 - 区域[%s] HTTP状态码: %ld, 响应: %s", 
                               region_id.c_str(), response_code, response_string.c_str());
                    if (config.verbose) {
                        cout << "Alarm request failed with HTTP code: " << response_code
                             << ", response: " << response_string << endl;
                    }
                }
            } else {
                LOG_ERROR_F("报警接口网络请求失败 - 区域[%s] 错误: %s", region_id.c_str(), curl_easy_strerror(res));
                if (config.verbose) {
                    cout << "Alarm request failed: " << curl_easy_strerror(res) << endl;
                }
            }

            // 清理
            curl_slist_free_all(headers);

        } catch (const exception& e) {
            LOG_ERROR_F("报警接口调用异常 - 区域[%s] 异常: %s", region_id.c_str(), e.what());
            if (config.verbose) {
                cout << "Exception in sendAlarmRequest: " << e.what() << endl;
            }
        }

        curl_easy_cleanup(curl);
    }

    return success;
}

/**
 * @Description: 将图片编码为base64字符串
 */
string RegionDetectionManager::encodeImageToBase64(const cv::Mat& image) {
    vector<uchar> buffer;
    vector<int> params = {cv::IMWRITE_JPEG_QUALITY, 80}; // 设置JPEG质量为80%

    if (!cv::imencode(".jpg", image, buffer, params)) {
        if (config.verbose) {
            cout << "Failed to encode image to JPEG" << endl;
        }
        return "";
    }

    // 简单的base64编码实现
    const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";
    string result;
    int val = 0, valb = -6;

    for (uchar c : buffer) {
        val = (val << 8) + c;
        valb += 8;
        while (valb >= 0) {
            result.push_back(chars[(val >> valb) & 0x3F]);
            valb -= 6;
        }
    }

    if (valb > -6) {
        result.push_back(chars[((val << 8) >> (valb + 8)) & 0x3F]);
    }

    while (result.size() % 4) {
        result.push_back('=');
    }

    return result;
}

/**
 * @Description: 获取当前时间字符串
 */
string RegionDetectionManager::getCurrentTimeString() {
    auto now = chrono::system_clock::now();
    auto time_t = chrono::system_clock::to_time_t(now);
    auto tm = *localtime(&time_t);

    stringstream ss;
    ss << put_time(&tm, "%Y-%m-%d %H:%M:%S");
    return ss.str();
}

/**
 * @Description: 记录报警日志
 */
void RegionDetectionManager::logAlarm(const string& region_name, float confidence) {
    string timestamp = getCurrentTimeString();
    cout << "[" << timestamp << "] 区域[" << region_name << "] 检测到大渣，置信度: "
         << fixed << setprecision(2) << confidence << endl;
}
