/*
 * @Author: <PERSON> RF
 * @Date: 2025-01-14 19:05:32
 * @LastEditors: Li RF
 * @LastEditTime: 2025-03-22 16:54:35
 * @Description: 
 * Email: <EMAIL>
 * Copyright (c) 2025 Li RF, All Rights Reserved.
 */
#include <iostream>
#include <cstdlib>
#include <getopt.h>
#include <fstream>
#include <sstream>

#include "parse_config.hpp"

/**
 * @Description: 检查输入源是否存在（支持本地文件和网络流）
 * @param {string&} name:
 * @return {*}
 */
static bool isInputSourceValid(string& name) {
    // 检查是否为网络流（RTSP、HTTP、HTTPS等）
    if (name.find("rtsp://") == 0 ||
        name.find("http://") == 0 ||
        name.find("https://") == 0 ||
        name.find("udp://") == 0 ||
        name.find("tcp://") == 0) {
        // 对于网络流，我们假设它是有效的，实际连接会在FFmpeg中验证
        return true;
    }

    // 对于本地文件，检查文件是否存在
    ifstream f(name.c_str());
    return f.good();
}


/**
 * @Description: 显示帮助信息
 * @param {char} *program_name: 程序名称
 * @return {*}
 */
void ConfigParser::print_help(const string &program_name) const { // 常量成员函数承诺不会修改调用它的对象的任何成员变量（函数内只有只读操作）
    cout << "Usage: " << program_name << " [options]" << endl;
    cout << "Options:" << endl;
    cout << "  -m, --model_path <string, require> || Set rknn model path. need to be set" << endl;
    cout << "  -i, --input <int or string, require> || Set input source. int: Camera index, like 0; String: video path. need to be set" << endl;
    cout << "  -a, --accels_2d <int> || Configure the 2D acceleration mode. 1:opencv, 2:RGA. default: 2" << endl;
    cout << "  -t, --threads <string> || Set threads number. default: 1" << endl;
    cout << "  -c, --opencl <bool or int> || Configure the opencl mode. true(1):use opencl, fals(0):use cpu. default: True(1)" << endl;
    cout << "  -d, --decodec <string> || Set decoder. default: h264_rkmpp (option: h264)" << endl;
    cout << "  -r, --read_engine <int or string> || Set input sources read engine. default: 1:ffmpeg (option: 2:opencv)" << endl;
    cout << "  -y, --yolo_type <int> || Set YOLO model type. 5:YOLOv5, 8:YOLOv8. default: 5" << endl;
    cout << "  -l, --labels <string> || Set labels file path. default: use built-in COCO labels" << endl;
    cout << "  -s, --screen_fps || Show fps on screen" << endl;
    cout << "  -p, --print_fps || Print fps on console" << endl;
    cout << "  -v, --verbose || Enable verbose output" << endl;
    cout << "  -h, --help || Show this help message" << endl;
    cout << endl;
    cout << "Alarm Detection Options:" << endl;
    cout << "  --confidence <float> || Set confidence threshold for detection. default: 0.4" << endl;
    cout << "  --continuous <int> || Set continuous detection count threshold. default: 10" << endl;
    cout << "  --alarm_url <string> || Set alarm API URL for notifications" << endl;
    cout << "  --cooldown <int> || Set alarm cooldown time in minutes. default: 20" << endl;
    cout << "  --device_id <string> || Set device ID for alarm notifications" << endl;
    cout << "  --regions <string> || Set detection regions in format: id1,name1,device_id1,x1,y1,x2,y2,...;id2,name2,..." << endl;
    cout << "  --draw_regions || Draw region boundaries on output video. default: false" << endl;
    cout << "  --alarm_header <string> || Set alarm HTTP request headers in JSON format. default: {}" << endl;
    cout << "  --ground_calibration <string> || Set ground calibration rectangles in format: enabled,id1,name1,x1,y1,x2,y2,x3,y3,x4,y4;id2,..." << endl;
    cout << "  --enable_reconnect <bool> || Enable automatic reconnection. default: true" << endl;
    cout << "  --max_reconnect_attempts <int> || Set maximum reconnection attempts. default: 5" << endl;
    cout << "  --reconnect_delay <int> || Set reconnection delay in seconds. default: 5" << endl;
    cout << endl;
}

/**
 * @Description: 打印配置信息
 * @param {AppConfig} config: 配置信息
 * @return {*}
 */
// 打印配置信息
void ConfigParser::printConfig(const AppConfig &config) const {
    cout << "​*************************" << endl;
    cout << "Parse Information:" << endl;
    cout << "    Model path: " << config.model_path << endl;
    cout << "    Input source: " << config.input << endl;
    cout << "    Threads: " << config.threads << endl;
    cout << "    Opencl: " << boolalpha << config.opencl << endl; // boolalpha: 将 bool 类型以 true/false 形式输出
    cout << "    Decodec: " << config.decodec << endl;
    cout << "    Screen fps: " << boolalpha << config.screen_fps << endl;
    cout << "    Console fps: " << boolalpha << config.print_fps << endl;

    if (config.accels_2d == ACCELS_2D::ACC_OPENCV)
        cout << "    Accels_2d: opencv"<< endl;
    else if (config.accels_2d == ACCELS_2D::ACC_RGA)
        cout << "    Accels_2d: RGA" << endl;

    if (config.read_engine == READ_ENGINE::EN_FFMPEG)
        cout << "    Read engine: ffmpeg" << endl;
    else if (config.read_engine == READ_ENGINE::EN_OPENCV)
        cout << "    Read engine: opencv" << endl;

    if (config.yolo_model_type == YOLO_MODEL_TYPE::YOLO_V5)
        cout << "    YOLO model type: YOLOv5" << endl;
    else if (config.yolo_model_type == YOLO_MODEL_TYPE::YOLO_V8)
        cout << "    YOLO model type: YOLOv8" << endl;

    if (!config.labels_path.empty())
        cout << "    Labels file: " << config.labels_path << endl;
    else
        cout << "    Labels file: built-in COCO labels" << endl;


}

/**
 * @Description: 解析命令行参数
 * @param {int} argc: 命令行参数个数
 * @param {char} *argv: 命令行参数数组
 * @return {*}
 */
AppConfig ConfigParser::parse_arguments(int argc, char *argv[]) const {
    if (argc < 2) {
        this->print_help(argv[0]);
        exit(EXIT_FAILURE);
    }
    AppConfig config;


    /* 定义长选项 */ 
    struct option long_options[] = {
        {"model_path", required_argument, nullptr, 'm'},
        {"input",      required_argument, nullptr, 'i'},
        {"accels_2d",  optional_argument, nullptr, 'a'},
        {"threads",    optional_argument, nullptr, 't'},
        {"opencl",     optional_argument, nullptr, 'c'},
        {"decodec",    optional_argument, nullptr, 'd'},
        {"read_engine",optional_argument, nullptr, 'r'},
        {"yolo_type",  optional_argument, nullptr, 'y'},
        {"labels",     optional_argument, nullptr, 'l'},
        {"screen_fps",   no_argument,       nullptr, 's'},
        {"print_fps",  no_argument,       nullptr, 'p'},
        {"verbose",    no_argument,       nullptr, 'v'},
        {"help",       no_argument,       nullptr, 'h'},
        // 报警检测相关参数
        {"confidence", required_argument, nullptr, 1001},
        {"continuous", required_argument, nullptr, 1002},
        {"alarm_url",  required_argument, nullptr, 1003},
        {"cooldown",   required_argument, nullptr, 1004},
        {"device_id",  required_argument, nullptr, 1005},
        {"regions",    required_argument, nullptr, 1006},
        {"draw_regions", no_argument,     nullptr, 1007},
        {"alarm_header", required_argument, nullptr, 1008},
        {"reset_cooldown_minutes", required_argument, nullptr, 1009},
        {"instance_name", required_argument, nullptr, 1010}, // 实例名称
        {"log_enable_file", required_argument, nullptr, 1011},
        {"log_enable_console", required_argument, nullptr, 1012},
        {"log_min_level", required_argument, nullptr, 1013},
        {"log_retention_days", required_argument, nullptr, 1014},
        {"ground_calibration", required_argument, nullptr, 1015}, // 平面标定配置
        {"enable_reconnect", required_argument, nullptr, 1016}, // 启用自动重连
        {"max_reconnect_attempts", required_argument, nullptr, 1017}, // 最大重连次数
        {"reconnect_delay", required_argument, nullptr, 1018}, // 重连延迟
        {nullptr,      0,                 nullptr, 0}
    };

    /* 解析参数 */ 
    int opt;
    // 支持短选项和长选项
    // : 表示该选项需要一个参数，v 和 h 不需要
    // 如果解析到长选项，返回 val 字段的值（即第四列）
    while ((opt = getopt_long(argc, argv, "m:i:a:t:c:d:r:y:l:spvh", long_options, nullptr)) != -1) {
        string temp_optarg = "";
        // 拷贝防止被修改
        if (optarg)
            temp_optarg = optarg;

        switch (opt) {
            case 'm': {
                if (!optarg) {
                    cerr << "Error: Missing argument for option: " << static_cast<char>(opt) << endl;
                    exit(EXIT_FAILURE);
                }
                // 检查文件是否存在
                if (!isInputSourceValid(temp_optarg)) {
                    cerr << "Error: File not found: " << temp_optarg << endl;
                    exit(EXIT_FAILURE);
                }
                config.model_path = temp_optarg;
                break;
            }
            case 'i': {
                if (!optarg) {
                    cerr << "Error: Missing argument for option: " << static_cast<char>(opt) << endl;
                    exit(EXIT_FAILURE);
                }
                try {
                    if (temp_optarg.size() == 1) { // 摄像头标号
                        if (isdigit(temp_optarg[0])) 
                            config.input_format = INPUT_FORMAT::IN_CAMERA;
                        else
                            throw invalid_argument("Invalid camera index.");
                    } else{ // 视频路径
                        config.input_format = INPUT_FORMAT::IN_VIDEO;
                    }
                } catch (const exception &e) {
                    exit(EXIT_FAILURE);
                }
                // 检查输入源是否有效（支持本地文件和网络流）
                if (!isInputSourceValid(temp_optarg)) {
                    cerr << "Error: Input source not found or invalid: " << temp_optarg << endl;
                    exit(EXIT_FAILURE);
                }
                config.input = temp_optarg;
                break;
            }
            case 'a': {
                if (!optarg) {
                    cerr << "Error: Missing argument for option: " << static_cast<char>(opt) << endl;
                    exit(EXIT_FAILURE);
                }
                try {
                    config.accels_2d = stoi(temp_optarg);
                    if (config.accels_2d != ACCELS_2D::ACC_OPENCV && config.accels_2d != ACCELS_2D::ACC_RGA) 
                        throw invalid_argument("Unsupported hwaccel type.");
                } catch (const exception &e) {
                    exit(EXIT_FAILURE);
                }
                break;
            }
            case 't': {
                if (!optarg) {
                    cerr << "Error: Missing argument for option: " << static_cast<char>(opt) << endl;
                    exit(EXIT_FAILURE);
                }
                config.threads = stoi(temp_optarg);
                break;
            }
            case 'c': {
                if (temp_optarg == "true" || temp_optarg == "1")
                    config.opencl = true;
                else if (temp_optarg == "false" || temp_optarg == "0") 
                    config.opencl = false;
                else {
                    cerr << "Error: Invalid argument for option: " << static_cast<char>(opt) << endl;
                    exit(EXIT_FAILURE);
                }
                break;
            }
            case 'd': {
                if (!optarg) {
                    cerr << "Error: Missing argument for option: " << static_cast<char>(opt) << endl;
                    exit(EXIT_FAILURE);
                }
                config.decodec = temp_optarg;
                break;
            }
            case 'r': {
                if (temp_optarg == "ffmpeg" || temp_optarg == "1")
                    config.read_engine = READ_ENGINE::EN_FFMPEG;
                else if (temp_optarg == "opencv" || temp_optarg == "2")
                    config.read_engine = READ_ENGINE::EN_OPENCV;
                else {
                    cerr << "Error: Unsupported read engine." << endl;
                    exit(EXIT_FAILURE);
                }
                break;
            }
            case 'y': {
                if (!optarg) {
                    cerr << "Error: Missing argument for option: " << static_cast<char>(opt) << endl;
                    exit(EXIT_FAILURE);
                }
                if (temp_optarg == "5")
                    config.yolo_model_type = YOLO_MODEL_TYPE::YOLO_V5;
                else if (temp_optarg == "8")
                    config.yolo_model_type = YOLO_MODEL_TYPE::YOLO_V8;
                else {
                    cerr << "Error: Unsupported YOLO model type. Use 5 for YOLOv5 or 8 for YOLOv8." << endl;
                    exit(EXIT_FAILURE);
                }
                break;
            }
            case 'l': {
                if (!optarg) {
                    cerr << "Error: Missing argument for option: " << static_cast<char>(opt) << endl;
                    exit(EXIT_FAILURE);
                }
                config.labels_path = temp_optarg;
                break;
            }
            case 's':
                config.screen_fps = true;
                break;
            case 'p':
                config.print_fps = true;
                break;
            case 'v':
                config.verbose = true;
                break;
            case 'h':
                this->print_help(argv[0]);
                exit(EXIT_SUCCESS);
            // 报警检测相关参数
            case 1001: // --confidence
                config.confidence_threshold = stof(temp_optarg);
                break;
            case 1002: // --continuous
                config.continuous_count = stoi(temp_optarg);
                break;
            case 1003: // --alarm_url
                config.alarm_url = temp_optarg;
                break;
            case 1004: // --cooldown
                config.cooldown_minutes = stoi(temp_optarg);
                break;
            case 1005: // --device_id
                config.device_id = temp_optarg;
                break;
            case 1006: // --regions
                parseRegionsString(temp_optarg, config);
                break;
            case 1007: // --draw_regions
                config.draw_regions = true;
                break;
            case 1008: // --alarm_header
                config.alarm_header = temp_optarg;
                break;
            case 1009: // --reset_cooldown_minutes
                config.progressive_cooldown.enabled = true;
                config.progressive_cooldown.reset_cooldown_minutes = stof(temp_optarg);
                break;
            case 1010: // --instance_name
                config.instance_name = temp_optarg;
                break;
            case 1011: // --log_enable_file
                config.log_enable_file = (temp_optarg == "true");
                break;
            case 1012: // --log_enable_console
                config.log_enable_console = (temp_optarg == "true");
                break;
            case 1013: // --log_min_level
                config.log_min_level = temp_optarg;
                break;
            case 1014: // --log_retention_days
                config.log_retention_days = stoi(temp_optarg);
                break;
            case 1015: // --ground_calibration
                // 平面标定现在通过配置文件加载，此选项暂时保留但不处理
                cout << "Warning: --ground_calibration option is deprecated, use configuration file instead" << endl;
                break;
            case 1016: // --enable_reconnect
                config.enable_reconnect = (temp_optarg == "true" || temp_optarg == "1");
                break;
            case 1017: // --max_reconnect_attempts
                config.max_reconnect_attempts = stoi(temp_optarg);
                break;
            case 1018: // --reconnect_delay
                config.reconnect_delay_seconds = stoi(temp_optarg);
                break;
            default:
                this->print_help(argv[0]);
                exit(EXIT_FAILURE);
        }
    }
    if (config.verbose)
        this->printConfig(config);

    return config;
}

/**
 * @Description: 解析区域字符串
 * @param {string&} regions_str: 区域字符串，格式：id1,name1,device_id1,x1,y1,x2,y2,...;id2,name2,...
 * @param {AppConfig&} config: 配置对象
 */
void ConfigParser::parseRegionsString(const string& regions_str, AppConfig& config) const {
    if (regions_str.empty()) {
        return;
    }

    // 按分号分割不同区域
    stringstream ss(regions_str);
    string region_str;

    while (getline(ss, region_str, ';')) {
        if (region_str.empty()) continue;

        // 按逗号分割区域参数
        stringstream region_ss(region_str);
        string item;
        vector<string> parts;

        while (getline(region_ss, item, ',')) {
            parts.push_back(item);
        }

        // 至少需要id, name, device_id和至少一个坐标点(x,y)
        if (parts.size() >= 5) {
            DetectionRegion region;
            region.id = parts[0];
            region.name = parts[1];
            region.device_id = parts[2];

            // 解析坐标点（从第3个元素开始，每两个为一对坐标）
            for (size_t i = 3; i + 1 < parts.size(); i += 2) {
                try {
                    int x = stoi(parts[i]);
                    int y = stoi(parts[i + 1]);
                    region.points.push_back(make_pair(x, y));
                } catch (const exception& e) {
                    cerr << "Warning: Invalid coordinate in region " << region.id << ": " << e.what() << endl;
                }
            }

            if (!region.points.empty()) {
                config.regions.push_back(region);
                if (config.verbose) {
                    cout << "Added region: " << region.id << " (" << region.name << ") with "
                         << region.points.size() << " points" << endl;
                }
            }
        } else {
            cerr << "Warning: Invalid region format: " << region_str << endl;
        }
    }
}

