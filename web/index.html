<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>大渣目标检测管理</title>
    <link href="assets/css/styles.css" rel="stylesheet">

    <script src="packages/konva.min.js"></script>
    <script defer src="packages/alpine.min.js"></script>
</head>

<body x-data="detectionManager()" x-init="init()">

    <!-- 顶部导航栏 -->
    <header class="header">
        <div class="container">
            <div class="header-content">
                <div class="logo-section">
                    <div class="logo">AI</div>
                    <div class="title-section">
                        <h1>大渣目标检测管理</h1>
                        <p>Industrial AI Detection Management Platform</p>
                    </div>
                </div>
                <div class="header-actions">
                    <div class="connection-status">
                        <div class="status-dot" :class="connectionStatus"></div>
                        <span class="status-text" x-text="connectionStatusText"></span>
                    </div>
                    <button @click="refreshData()" class="btn btn-outline">
                        <span style="margin-right: 6px;">⟳</span>刷新数据
                    </button>
                </div>
            </div>
        </div>
    </header>

    <!-- 系统概览 -->
    <section class="overview-section">
        <div class="container">
            <div class="stats-grid">
                <div class="stat-card">
                    <div class="stat-number primary" x-text="totalInstances"></div>
                    <div class="stat-label">总检测实例</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number success" x-text="runningInstances"></div>
                    <div class="stat-label">运行中</div>
                </div>
                <div class="stat-card">
                    <div class="stat-number danger" x-text="stoppedInstances"></div>
                    <div class="stat-label">已停止</div>
                </div>
                <div class="stat-card">
                    <div class="action-buttons">
                        <button @click="startAll()" class="btn btn-success">启动所有</button>
                        <button @click="stopAll()" class="btn btn-danger">停止所有</button>
                    </div>
                </div>
            </div>
        </div>
    </section>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <div class="container">

            <!-- 内容头部 -->
            <div class="content-header">
                <div class="content-title">
                    <h2>检测实例管理</h2>
                    <p>管理和监控所有目标检测实例的运行状态</p>
                </div>
                <div class="content-actions">
                    <button @click="showAddModal = true" class="btn btn-primary">
                        <span style="margin-right: 6px;">+</span>新增实例
                    </button>
                    <button @click="openSystemConfig()" class="btn btn-warning">
                        系统配置
                    </button>
                </div>
            </div>

            <!-- 实例列表 -->
            <div class="instances-grid" x-show="cameras.length > 0">
                <template x-for="camera in cameras" :key="camera.id">
                    <div class="instance-card">

                        <!-- 实例头部 -->
                        <div class="instance-header">
                            <div class="instance-info">
                                <h3 x-text="camera.name"></h3>
                                <div class="instance-id" x-text="camera.id"></div>
                            </div>
                            <span class="status-badge" :class="camera.status === 'running' ? 'running' : 'stopped'"
                                x-text="camera.status === 'running' ? '运行中' : '已停止'"></span>
                        </div>

                        <!-- 实例详情 -->
                        <div class="instance-details">
                            <div class="detail-row">
                                <span class="detail-label">输入源</span>
                                <span class="detail-value input-source" x-text="camera.input"
                                    :title="camera.input"></span>
                            </div>
                            <div class="detail-row">
                                <span class="detail-label">检测区域</span>
                                <span class="detail-value"
                                    x-text="(camera.regions ? camera.regions.length : 0) + ' 个'"></span>
                            </div>
                            <div class="detail-row" x-show="camera.status === 'running' && camera.uptime">
                                <span class="detail-label">运行时间</span>
                                <span class="detail-value" x-text="camera.uptime"></span>
                            </div>
                        </div>

                        <!-- 操作按钮 -->
                        <div class="instance-actions">
                            <button @click="toggleCamera(camera.id, camera.status)" class="btn"
                                :class="camera.status === 'running' ? 'btn-danger' : 'btn-success'"
                                x-text="camera.status === 'running' ? '停止' : '启动'"></button>

                            <button @click="editCamera(camera.id)" class="btn btn-primary">修改</button>

                            <button @click="editRegions(camera.id)" class="btn btn-warning">区域</button>

                            <button @click="editGroundCalibration(camera.id)" class="btn btn-secondary">📐 平面标定</button>

                            <button @click="openLogViewer(camera.id, camera.name)" class="btn btn-info">📋 日志</button>

                            <button @click="deleteCamera(camera.id, camera.name)" class="btn btn-secondary">删除</button>
                        </div>
                    </div>
                </template>
            </div>

            <!-- 空状态 -->
            <div class="empty-state" x-show="cameras.length === 0">
                <div class="empty-icon">📹</div>
                <h3>暂无检测实例</h3>
                <p>开始创建您的第一个目标检测实例</p>
                <button @click="showAddModal = true" class="btn btn-primary">
                    新增检测实例
                </button>
            </div>
        </div>
    </main>

    <!-- 底部状态栏 -->
    <footer class="footer">
        <div class="container">
            <div class="footer-content">
                <div x-text="lastUpdate"></div>
                <div>系统版本: v1.0.0</div>
            </div>
        </div>
    </footer>

    <!-- 消息提示 -->
    <div x-show="message.show" x-transition:enter="transition ease-out duration-300"
        x-transition:enter-start="opacity-0 transform translate-y-2"
        x-transition:enter-end="opacity-100 transform translate-y-0"
        x-transition:leave="transition ease-in duration-200"
        x-transition:leave-start="opacity-100 transform translate-y-0"
        x-transition:leave-end="opacity-0 transform translate-y-2" class="message-toast" :class="message.type">
        <span class="message-icon" x-text="message.type === 'success' ? '✓' : '✗'"></span>
        <span x-text="message.text"></span>
    </div>

    <!-- 新增实例模态框 -->
    <template x-if="showAddModal">
        <div class="modal-overlay" @click.self="showAddModal = false">
            <div class="modal">
                <div class="modal-header">
                    <h3 class="modal-title">新增检测实例</h3>
                    <button @click="showAddModal = false" class="modal-close">✕</button>
                </div>

                <form @submit.prevent="addCamera()">
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="form-label">实例ID</label>
                            <input type="text" x-model="newCamera.id" required class="form-input"
                                placeholder="例如: camera_001">
                            <div class="form-help">ID必须唯一</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">实例名称</label>
                            <input type="text" x-model="newCamera.name" required class="form-input"
                                placeholder="例如: 入口检测实例">
                        </div>

                        <div class="form-group">
                            <label class="form-label">输入源</label>
                            <input type="text" x-model="newCamera.input" required class="form-input"
                                placeholder="例如: rtsp://192.168.1.100:554/stream">
                        </div>

                        <div class="form-note">
                            <strong>提示:</strong> 检测区域可在创建实例后通过"区域"按钮进行配置
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" @click="showAddModal = false" class="btn btn-outline">
                            取消
                        </button>
                        <button type="submit" class="btn btn-primary">
                            创建实例
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </template>

    <!-- 修改实例模态框 -->
    <template x-if="showEditModal">
        <div class="modal-overlay" @click.self="showEditModal = false">
            <div class="modal">
                <div class="modal-header">
                    <h3 class="modal-title">修改检测实例</h3>
                    <button @click="showEditModal = false" class="modal-close">✕</button>
                </div>

                <form @submit.prevent="saveEditCamera()">
                    <div class="modal-body">
                        <div class="form-group">
                            <label class="form-label">实例ID</label>
                            <input type="text" x-model="editingCamera.id" required class="form-input"
                                placeholder="例如: camera_001">
                            <div class="form-help">ID必须唯一</div>
                        </div>

                        <div class="form-group">
                            <label class="form-label">实例名称</label>
                            <input type="text" x-model="editingCamera.name" required class="form-input"
                                placeholder="例如: 入口检测实例">
                        </div>

                        <div class="form-group">
                            <label class="form-label">输入源</label>
                            <input type="text" x-model="editingCamera.input" required class="form-input"
                                placeholder="例如: rtsp://192.168.1.100:554/stream">
                        </div>

                        <div class="form-note">
                            <strong>提示:</strong> 修改实例信息后，如果实例正在运行，需要重新启动才能生效
                        </div>
                    </div>

                    <div class="modal-footer">
                        <button type="button" @click="showEditModal = false; resetEditCamera()" class="btn btn-outline">
                            取消
                        </button>
                        <button type="submit" class="btn btn-primary">
                            保存修改
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </template>

    <!-- 删除确认模态框 -->
    <template x-if="showDeleteModal">
        <div class="modal-overlay" @click.self="showDeleteModal = false">
            <div class="modal">
                <div class="modal-header">
                    <h3 class="modal-title">确认删除</h3>
                    <button @click="showDeleteModal = false" class="modal-close">✕</button>
                </div>

                <div class="modal-body">
                    <p>确定要删除检测实例 "<strong x-text="deleteTarget.name"></strong>" 吗？</p>
                    <div class="form-warning" style="margin-top: 16px;">
                        <strong>警告:</strong> 此操作不可撤销，如果实例正在运行，将会被强制停止。
                    </div>
                </div>

                <div class="modal-footer">
                    <button @click="showDeleteModal = false" class="btn btn-outline">
                        取消
                    </button>
                    <button @click="confirmDelete()" class="btn btn-danger">
                        确认删除
                    </button>
                </div>
            </div>
        </div>
    </template>

    <!-- 日志查看器 -->
    <template x-if="showLogViewer">
        <div class="log-viewer-overlay">
            <div class="log-viewer-modal">
                <!-- 顶部工具栏 -->
                <div class="log-toolbar">
                    <div class="log-toolbar-left">
                        <h3 class="log-title">
                            <span class="log-icon">📋</span>
                            实例日志 - <span x-text="logInstanceName"></span>
                        </h3>
                    </div>
                    <div class="log-toolbar-center">
                        <div class="log-controls">
                            <input type="date" x-model="logDate" @change="loadLogs()" class="log-input">
                            <select x-model="logLevel" @change="filterLogs()" class="log-select">
                                <option value="">全部级别</option>
                                <option value="DEBUG">DEBUG</option>
                                <option value="INFO">INFO</option>
                                <option value="WARN">WARN</option>
                                <option value="ERROR">ERROR</option>
                            </select>
                            <input type="text" x-model="logSearchTerm" @input="searchLogs()"
                                   placeholder="🔍 搜索日志..." class="log-search">
                        </div>
                    </div>
                    <div class="log-toolbar-right">
                        <button @click="refreshLogs()" class="log-btn log-btn-refresh" title="刷新">
                            🔄
                        </button>
                        <button @click="closeLogViewer()" class="log-btn log-btn-close" title="关闭">
                            ✕
                        </button>
                    </div>
                </div>

                <!-- 主内容区 -->
                <div class="log-main">
                    <!-- 左侧统计面板 -->
                    <div class="log-sidebar">
                        <div class="log-stats-panel">
                            <h4>日志统计</h4>
                            <div class="stat-grid">
                                <div class="stat-card stat-total">
                                    <div class="stat-number" x-text="logStats.total"></div>
                                    <div class="stat-label">总计</div>
                                </div>
                                <div class="stat-card stat-info" x-show="logStats.info > 0">
                                    <div class="stat-number" x-text="logStats.info"></div>
                                    <div class="stat-label">INFO</div>
                                </div>
                                <div class="stat-card stat-warn" x-show="logStats.warn > 0">
                                    <div class="stat-number" x-text="logStats.warn"></div>
                                    <div class="stat-label">WARN</div>
                                </div>
                                <div class="stat-card stat-error" x-show="logStats.error > 0">
                                    <div class="stat-number" x-text="logStats.error"></div>
                                    <div class="stat-label">ERROR</div>
                                </div>
                                <div class="stat-card stat-debug" x-show="logStats.debug > 0">
                                    <div class="stat-number" x-text="logStats.debug"></div>
                                    <div class="stat-label">DEBUG</div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 右侧日志内容 -->
                    <div class="log-content">
                        <div x-show="logLoading" class="log-loading">
                            <div class="loading-spinner"></div>
                            <div class="loading-text">加载中...</div>
                        </div>
                        <div x-show="!logLoading" class="log-display-wrapper">
                            <pre x-ref="logCode" class="log-display"></pre>
                        </div>
                    </div>
                </div>

                <!-- 底部状态栏 -->
                <div class="log-footer">
                    <div class="log-status">
                        <span x-show="logStats.total > 0">
                            显示 <strong x-text="logStats.total"></strong> 条日志记录
                        </span>
                        <span x-show="logStats.total === 0 && !logLoading">
                            暂无日志记录
                        </span>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- 系统配置模态框 -->
    <template x-if="showSystemConfig">
        <div class="modal-overlay">
            <div class="modal system-config-modal">
                <div class="modal-header">
                    <h3>⚙️ 系统配置</h3>
                    <button @click="closeSystemConfig()" class="btn-close">✕</button>
                </div>

                <div class="modal-body">
                    <div class="config-tabs">
                        <button @click="activeConfigTab = 'detection'"
                                :class="activeConfigTab === 'detection' ? 'tab-active' : 'tab-inactive'"
                                class="config-tab">
                            🎯 检测配置
                        </button>
                        <button @click="activeConfigTab = 'alarm'"
                                :class="activeConfigTab === 'alarm' ? 'tab-active' : 'tab-inactive'"
                                class="config-tab">
                            🚨 报警配置
                        </button>
                        <button @click="activeConfigTab = 'log'"
                                :class="activeConfigTab === 'log' ? 'tab-active' : 'tab-inactive'"
                                class="config-tab">
                            📋 日志配置
                        </button>
                    </div>

                    <!-- 检测配置 -->
                    <div x-show="activeConfigTab === 'detection'" class="config-panel">
                        <div class="form-group">
                            <label class="form-label">
                                置信度阈值
                                <span class="help-icon" data-tooltip="置信度阈值说明：

• 置信度表示AI模型对检测结果的确信程度
• 范围：0.1（10%）到 1.0（100%）
• 只有置信度≥阈值的检测结果才会被采用

建议设置：
• 0.3-0.4：平衡检测率和误报率（推荐）
• 0.5-0.7：减少误报，但可能漏检
• 0.1-0.2：提高检测率，但误报较多

注意：阈值过低会增加误报，
阈值过高可能漏检真实目标">❓</span>
                            </label>
                            <input type="number" x-model="systemConfig.detection.confidence_threshold"
                                   min="0.1" max="1.0" step="0.1" class="form-input">
                            <p class="form-help">检测结果的最低置信度要求，范围0.1-1.0，建议0.3-0.7</p>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                连续检测次数
                                <span class="help-icon" data-tooltip="连续检测机制说明：

• 系统会对每一帧图像进行目标检测
• 只有连续N帧都检测到目标时才触发报警
• 如果中间有一帧没检测到，计数器重置为0

建议值：
• 高精度场景：3-5次（快速响应）
• 一般场景：8-15次（平衡准确性和响应速度）
• 高干扰场景：20-30次（减少误报）

注意：值越大响应越慢，但误报越少">❓</span>
                            </label>
                            <input type="number" x-model="systemConfig.detection.continuous_count"
                                   min="1" max="50" class="form-input">
                            <p class="form-help">触发报警前需要连续检测到目标的次数，避免误报</p>
                        </div>
                    </div>

                    <!-- 报警配置 -->
                    <div x-show="activeConfigTab === 'alarm'" class="config-panel">
                        <div class="form-group">
                            <label class="form-label">报警接口地址</label>
                            <input type="url" x-model="systemConfig.alarm.alarm_url"
                                   placeholder="http://your-api.com/alarm" class="form-input">
                            <p class="form-help">接收报警通知的API接口地址</p>
                        </div>

                        <div class="form-group">
                            <label class="form-label">请求头 - Tenant Key</label>
                            <input type="text" x-model="systemConfig.alarm.alarm_header.tenant_key"
                                   placeholder="租户密钥" class="form-input">
                            <p class="form-help">API请求时使用的租户认证密钥</p>
                        </div>

                        <div class="form-group">
                            <label class="form-label">冷却时间 (分钟)</label>
                            <input type="number" x-model="systemConfig.alarm.cooldown_minute"
                                   min="1" max="120" class="form-input">
                            <p class="form-help">同一区域触发报警后的冷却时间，避免频繁报警</p>
                        </div>

                        <div class="form-group">
                            <label class="form-label">
                                <input type="checkbox" x-model="systemConfig.alarm.progressive_cooldown.enabled" class="form-checkbox">
                                启用渐进式冷却
                                <span class="help-icon" data-tooltip="渐进式冷却机制说明：

工作原理：
• 触发报警后进入基础冷却期（如20分钟）
• 如果期间没有检测到目标，冷却时间会逐渐减少
• 无目标时间越长，剩余冷却时间越短

计算公式：
• 剩余冷却 = 基础冷却 × (1 - 无目标时间/重置时间)
• 例：基础20分钟，重置5分钟，无目标2分钟
• 剩余冷却 = 20 × (1 - 2/5) = 12分钟

完全重置：
• 当无目标时间 ≥ 重置时间时，冷却期立即结束
• 可以立即触发新的报警

实际效果：目标消失后快速恢复监控能力">❓</span>
                            </label>
                            <p class="form-help">开启后，无目标时冷却时间逐渐减少，完全无目标时立即重置</p>
                        </div>

                        <div class="form-group" x-show="systemConfig.alarm.progressive_cooldown.enabled">
                            <label class="form-label">
                                冷却重置时间 (分钟)
                                <span class="help-icon" data-tooltip="冷却重置时间说明：

重置机制：
• 当连续N分钟没有检测到目标时，冷却期立即结束
• 在此时间内，冷却时间按比例线性减少
• 无目标时间越长，剩余冷却时间越短

实际应用：
• 设置5分钟：目标消失5分钟后可立即重新报警
• 设置1分钟：目标消失1分钟后可立即重新报警
• 设置0.1分钟：目标消失6秒后可立即重新报警

建议设置：
• 间歇性目标：1-5分钟（快速响应）
• 持续性监控：5-15分钟（避免误报）
• 长期异常：30-60分钟（确保异常消失）

注意：时间越短，系统响应越敏感">❓</span>
                            </label>
                            <input type="number" x-model="systemConfig.alarm.progressive_cooldown.reset_cooldown_minutes"
                                   min="0.1" max="60" step="0.1" class="form-input">
                            <p class="form-help">连续多长时间没有目标时完全重置冷却期（立即结束冷却）</p>
                        </div>
                    </div>

                    <!-- 日志配置 -->
                    <div x-show="activeConfigTab === 'log'" class="config-panel">
                        <div class="form-group">
                            <label class="form-label">日志保留天数</label>
                            <input type="number" x-model="systemConfig.log.retention_days"
                                   min="1" max="365" class="form-input">
                            <p class="form-help">超过此天数的日志文件将被自动清理，建议7-30天</p>
                        </div>

                        <div class="config-info">
                            <h4>📝 其他日志设置</h4>
                            <div class="info-grid">
                                <div class="info-item">
                                    <span class="info-label">文件日志</span>
                                    <span class="info-value">✅ 已启用</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">控制台日志</span>
                                    <span class="info-value">✅ 已启用</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">日志级别</span>
                                    <span class="info-value">INFO</span>
                                </div>
                                <div class="info-item">
                                    <span class="info-label">日志格式</span>
                                    <span class="info-value">文本格式</span>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button @click="closeSystemConfig()" class="btn btn-secondary">取消</button>
                    <button @click="saveSystemConfig()" class="btn btn-primary">保存配置</button>
                </div>
            </div>
        </div>
    </template>

    <!-- 区域编辑器 -->
    <template x-if="showRegionEditor">
        <div class="region-editor">
            <!-- 头部工具栏 -->
            <div class="region-editor-header">
                <div class="region-editor-title">
                    区域编辑器 - <span x-text="editingRegionCamera.name"></span>
                </div>
                <div class="region-editor-controls">
                    <button @click="saveRegions()" class="btn btn-success">
                        保存区域
                    </button>
                    <button @click="closeRegionEditor()" class="btn btn-outline">
                        关闭
                    </button>
                </div>
            </div>

            <!-- 主体区域 -->
            <div class="region-editor-body">
                <!-- 画布容器 -->
                <div class="region-canvas-container">
                    <div class="canvas-overlay">
                        <span x-text="editingRegionCamera.input"></span>
                    </div>

                    <div id="region-canvas-stage"></div>

                    <div x-show="showInstructions" class="canvas-instructions">
                        <div class="instructions-header">
                            <h4>操作说明</h4>
                            <button @click="showInstructions = false" class="instructions-close">
                                ✕
                            </button>
                        </div>
                        <ul>
                            <li>点击"新增区域"开始绘制</li>
                            <li>在画布上点击创建多边形顶点</li>
                            <li><strong>点击起点（青色圆点）完成绘制</strong></li>
                            <li><strong>右键取消当前绘制</strong></li>
                            <li>拖拽红色顶点可以调整区域形状</li>
                            <li>点击区域可以选中并编辑</li>
                            <li>点击"刷新画面"获取最新视频帧</li>
                        </ul>
                    </div>

                    <!-- 显示操作说明按钮 -->
                    <div x-show="!showInstructions" class="show-instructions-btn">
                        <button @click="showInstructions = true" class="btn btn-outline">
                            ? 操作说明
                        </button>
                    </div>
                </div>

                <!-- 侧边栏 -->
                <div class="region-sidebar">
                    <div class="region-sidebar-header">
                        <div class="region-sidebar-title">检测区域列表</div>
                        <div class="text-sm text-gray-600">
                            共 <span x-text="regionList.length"></span> 个区域
                        </div>
                    </div>

                    <div class="region-sidebar-body">
                        <!-- 区域列表 -->
                        <ul class="region-list">
                            <template x-for="region in regionList" :key="region.id">
                                <li class="region-item" :class="selectedRegionId === region.id ? 'active' : ''"
                                    @click="selectRegion(region.id)">
                                    <div class="region-item-header">
                                        <div>
                                            <div class="region-item-name" x-text="region.name"></div>
                                            <div class="region-item-id" x-text="region.id"></div>
                                        </div>
                                        <div class="region-item-actions">
                                            <button @click.stop="editRegionInfo(region.id)"
                                                class="btn btn-primary">编辑</button>
                                            <button @click.stop="deleteRegion(region.id)"
                                                class="btn btn-danger">删除</button>
                                        </div>
                                    </div>
                                    <div class="region-item-info">
                                        <div><span x-text="region.points.length"></span> 个顶点</div>
                                        <div class="text-xs text-gray-500 mt-1">
                                            设备ID: <span x-text="region.device_id || '未设置'"></span>
                                        </div>
                                    </div>
                                </li>
                            </template>
                        </ul>

                        <!-- 空状态 -->
                        <div x-show="regionList.length === 0" class="text-center py-8 text-gray-500">
                            <div class="text-2xl mb-2">📍</div>
                            <div>暂无检测区域</div>
                            <div class="text-sm">点击下方按钮开始绘制</div>
                        </div>
                    </div>

                    <!-- 工具栏 -->
                    <div class="region-tools">
                        <div class="region-tools-grid">
                            <button @click="startDrawing()" class="btn btn-primary" :disabled="isDrawing">
                                <span x-text="isDrawing ? '绘制中...' : '新增区域'"></span>
                            </button>
                            <button @click="refreshVideoFrame()" class="btn btn-secondary">
                                刷新画面
                            </button>
                            <button @click="clearCanvas()" class="btn btn-warning">
                                清空区域
                            </button>
                        </div>

                        <!-- 新增区域表单 -->
                        <div x-show="showRegionForm" class="region-form">
                            <div class="form-group">
                                <label class="form-label">区域名称</label>
                                <input type="text" x-model="newRegionName" class="form-input" placeholder="例如: 入口区域">
                            </div>
                            <div class="form-group">
                                <label class="form-label">设备ID</label>
                                <input type="text" x-model="newRegionDeviceID" class="form-input"
                                    placeholder="例如: device_001">
                                <div class="form-help">设备的唯一标识符，用于区分不同的检测设备</div>
                            </div>
                            <div class="flex gap-2">
                                <button @click="confirmNewRegion()" class="btn btn-success">
                                    确认添加
                                </button>
                                <button @click="cancelNewRegion()" class="btn btn-outline">
                                    取消
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- 平面标定编辑器 -->
    <template x-if="showGroundCalibrationEditor">
        <div class="region-editor">
            <!-- 头部工具栏 -->
            <div class="region-editor-header">
                <div class="region-editor-title">
                    平面标定编辑器 - <span x-text="editingGroundCalibrationCamera.name"></span>
                </div>
                <div class="region-editor-controls">
                    <button @click="saveGroundCalibration()" class="btn btn-success">
                        保存标定
                    </button>
                    <button @click="closeGroundCalibrationEditor()" class="btn btn-outline">
                        关闭
                    </button>
                </div>
            </div>

            <!-- 主体区域 -->
            <div class="region-editor-body">
                <!-- 画布容器 -->
                <div class="region-canvas-container">
                    <div class="canvas-overlay">
                        <span x-text="editingGroundCalibrationCamera.input"></span>
                    </div>

                    <div id="ground-calibration-canvas-stage"></div>

                    <div x-show="showGroundInstructions" class="canvas-instructions">
                        <div class="instructions-header">
                            <h4>平面标定操作说明</h4>
                            <button @click="showGroundInstructions = false" class="instructions-close">
                                ✕
                            </button>
                        </div>
                        <div class="instructions-content">
                            <div class="instruction-item">
                                <span class="instruction-number">1</span>
                                <span class="instruction-text">点击"新增矩形"开始绘制地面参考矩形</span>
                            </div>
                            <div class="instruction-item">
                                <span class="instruction-number">2</span>
                                <span class="instruction-text">按顺序点击四个角点绘制矩形</span>
                            </div>
                            <div class="instruction-item">
                                <span class="instruction-number">3</span>
                                <span class="instruction-text">输入矩形名称和描述信息</span>
                            </div>
                            <div class="instruction-item">
                                <span class="instruction-number">4</span>
                                <span class="instruction-text">拖拽顶点可以调整矩形位置和大小</span>
                            </div>
                            <div class="instruction-item">
                                <span class="instruction-number">5</span>
                                <span class="instruction-text">点击"保存标定"完成设置</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 侧边栏 -->
                <div class="region-sidebar">
                    <div class="region-sidebar-header">
                        <div class="region-sidebar-title">平面标定矩形</div>
                        <div class="text-sm text-gray-600">
                            共 <span x-text="groundRectangleList.length"></span> 个矩形
                        </div>
                    </div>

                    <div class="region-sidebar-body">
                        <!-- 矩形列表 -->
                        <ul class="region-list">
                            <template x-for="rectangle in groundRectangleList" :key="rectangle.id">
                                <li class="region-item" :class="selectedGroundRectangleId === rectangle.id ? 'active' : ''"
                                    @click="selectGroundRectangle(rectangle.id)">
                                    <div class="region-item-header">
                                        <div>
                                            <div class="region-item-name" x-text="rectangle.name"></div>
                                            <div class="region-item-id" x-text="rectangle.id"></div>
                                        </div>
                                        <div class="region-item-actions">
                                            <button @click.stop="editGroundRectangleInfo(rectangle.id)"
                                                class="btn btn-primary">编辑</button>
                                            <button @click.stop="deleteGroundRectangle(rectangle.id)"
                                                class="btn btn-danger">删除</button>
                                        </div>
                                    </div>
                                    <div class="region-item-info">
                                        <div class="text-xs text-gray-500 mt-1" x-text="rectangle.description || '无描述'"></div>
                                    </div>
                                </li>
                            </template>
                        </ul>

                        <!-- 空状态 -->
                        <div x-show="groundRectangleList.length === 0" class="text-center py-8 text-gray-500">
                            <div class="text-2xl mb-2">📐</div>
                            <div>暂无平面标定矩形</div>
                            <div class="text-sm">点击下方按钮开始绘制</div>
                        </div>
                    </div>

                    <!-- 工具栏 -->
                    <div class="region-tools">
                        <div class="region-tools-grid">
                            <button @click="startDrawingGroundRectangle()" class="btn btn-primary" :disabled="isDrawingGround">
                                <span x-text="isDrawingGround ? '绘制中...' : '新增矩形'"></span>
                            </button>
                            <button @click="refreshGroundVideoFrame()" class="btn btn-secondary">
                                刷新画面
                            </button>
                            <button @click="clearGroundCanvas()" class="btn btn-warning">
                                清空矩形
                            </button>
                        </div>

                        <!-- 新增矩形表单 -->
                        <div x-show="showGroundRectangleForm" class="region-form">
                            <div class="form-group">
                                <label class="form-label">矩形名称</label>
                                <input type="text" x-model="newGroundRectangleName" class="form-input" placeholder="例如: 地面参考区域">
                            </div>
                            <div class="form-group">
                                <label class="form-label">描述 (可选)</label>
                                <input type="text" x-model="newGroundRectangleDescription" class="form-input" placeholder="例如: 地面参考区域">
                            </div>
                            <div class="flex gap-2">
                                <button @click="confirmNewGroundRectangle()" class="btn btn-success">
                                    确认添加
                                </button>
                                <button @click="cancelNewGroundRectangle()" class="btn btn-outline">
                                    取消
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </template>

    <!-- 编辑区域模态框 -->
    <template x-if="showEditRegionModal">
        <div class="modal-overlay" @click="cancelEditRegion()">
            <div class="modal" @click.stop>
                <div class="modal-header">
                    <h3 class="modal-title">编辑区域信息</h3>
                    <button @click="cancelEditRegion()" class="modal-close">✕</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label">区域名称</label>
                        <input type="text" x-model="editingRegion.name" class="form-input" placeholder="例如: 入口区域">
                    </div>
                    <div class="form-group">
                        <label class="form-label">设备ID</label>
                        <input type="text" x-model="editingRegion.device_id" class="form-input"
                            placeholder="例如: device_001">
                        <div class="form-help">设备的唯一标识符，用于区分不同的检测设备</div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button @click="confirmEditRegion()" class="btn btn-primary">
                        保存更改
                    </button>
                    <button @click="cancelEditRegion()" class="btn btn-outline">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </template>

    <!-- 删除区域确认模态框 -->
    <template x-if="showDeleteRegionModal">
        <div class="modal-overlay" @click="cancelDeleteRegion()">
            <div class="modal" @click.stop>
                <div class="modal-header">
                    <h3 class="modal-title">确认删除区域</h3>
                    <button @click="cancelDeleteRegion()" class="modal-close">✕</button>
                </div>
                <div class="modal-body">
                    <div class="delete-confirmation">
                        <div class="delete-icon">⚠️</div>
                        <p>确定要删除以下区域吗？</p>
                        <div class="delete-info">
                            <div><strong>区域名称:</strong> <span x-text="deletingRegion.name"></span></div>
                            <div><strong>设备ID:</strong> <span x-text="deletingRegion.device_id"></span></div>
                        </div>
                        <p class="delete-warning">此操作不可撤销！</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button @click="confirmDeleteRegion()" class="btn btn-danger">
                        确认删除
                    </button>
                    <button @click="cancelDeleteRegion()" class="btn btn-outline">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </template>

    <!-- 清空画布确认模态框 -->
    <template x-if="showClearCanvasModal">
        <div class="modal-overlay" @click="cancelClearCanvas()">
            <div class="modal" @click.stop>
                <div class="modal-header">
                    <h3 class="modal-title">确认清空画布</h3>
                    <button @click="cancelClearCanvas()" class="modal-close">✕</button>
                </div>
                <div class="modal-body">
                    <div class="delete-confirmation">
                        <div class="delete-icon">⚠️</div>
                        <p>确定要清空画布上的所有区域吗？</p>
                        <div class="delete-info">
                            <div>当前共有 <strong x-text="regionList.length"></strong> 个区域</div>
                        </div>
                        <p class="delete-warning">此操作将删除所有区域，且不可撤销！</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button @click="confirmClearCanvas()" class="btn btn-danger">
                        确认清空
                    </button>
                    <button @click="cancelClearCanvas()" class="btn btn-outline">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </template>

    <!-- 编辑地面矩形模态框 -->
    <template x-if="showEditGroundRectangleModal">
        <div class="modal-overlay" @click="cancelEditGroundRectangle()">
            <div class="modal" @click.stop>
                <div class="modal-header">
                    <h3 class="modal-title">编辑地面矩形信息</h3>
                    <button @click="cancelEditGroundRectangle()" class="modal-close">✕</button>
                </div>
                <div class="modal-body">
                    <div class="form-group">
                        <label class="form-label">矩形名称</label>
                        <input type="text" x-model="editingGroundRectangle.name" class="form-input" placeholder="例如: 地面参考区域">
                    </div>
                    <div class="form-group">
                        <label class="form-label">描述 (可选)</label>
                        <input type="text" x-model="editingGroundRectangle.description" class="form-input" placeholder="例如: 地面参考区域">
                    </div>
                </div>
                <div class="modal-footer">
                    <button @click="confirmEditGroundRectangle()" class="btn btn-primary">
                        保存更改
                    </button>
                    <button @click="cancelEditGroundRectangle()" class="btn btn-outline">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </template>

    <!-- 删除地面矩形确认模态框 -->
    <template x-if="showDeleteGroundRectangleModal">
        <div class="modal-overlay" @click="cancelDeleteGroundRectangle()">
            <div class="modal" @click.stop>
                <div class="modal-header">
                    <h3 class="modal-title">确认删除地面矩形</h3>
                    <button @click="cancelDeleteGroundRectangle()" class="modal-close">✕</button>
                </div>
                <div class="modal-body">
                    <div class="delete-confirmation">
                        <div class="delete-icon">⚠️</div>
                        <p>确定要删除以下地面矩形吗？</p>
                        <div class="delete-info">
                            <div><strong>矩形名称:</strong> <span x-text="deletingGroundRectangle.name"></span></div>
                        </div>
                        <p class="delete-warning">此操作不可撤销！</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button @click="confirmDeleteGroundRectangle()" class="btn btn-danger">
                        确认删除
                    </button>
                    <button @click="cancelDeleteGroundRectangle()" class="btn btn-outline">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </template>

    <!-- 清空地面矩形确认模态框 -->
    <template x-if="showClearGroundCanvasModal">
        <div class="modal-overlay" @click="cancelClearGroundCanvas()">
            <div class="modal" @click.stop>
                <div class="modal-header">
                    <h3 class="modal-title">确认清空地面矩形</h3>
                    <button @click="cancelClearGroundCanvas()" class="modal-close">✕</button>
                </div>
                <div class="modal-body">
                    <div class="delete-confirmation">
                        <div class="delete-icon">⚠️</div>
                        <p>确定要清空画布上的所有地面矩形吗？</p>
                        <div class="delete-info">
                            <div>当前共有 <strong x-text="groundRectangleList.length"></strong> 个地面矩形</div>
                        </div>
                        <p class="delete-warning">此操作将删除所有地面矩形，且不可撤销！</p>
                    </div>
                </div>
                <div class="modal-footer">
                    <button @click="confirmClearGroundCanvas()" class="btn btn-danger">
                        确认清空
                    </button>
                    <button @click="cancelClearGroundCanvas()" class="btn btn-outline">
                        取消
                    </button>
                </div>
            </div>
        </div>
    </template>

    <script src="assets/js/app.js?v=4"></script>
    <script>
        // 调试：检查函数是否存在
        window.addEventListener('alpine:init', () => {
            console.log('🎯 Alpine.js 已初始化');
            console.log('detectionManager 函数:', typeof window.detectionManager);
        });

        document.addEventListener('DOMContentLoaded', function() {
            setTimeout(() => {
                console.log('🔍 检查 editGroundCalibration 函数...');
                console.log('window.detectionManager:', typeof window.detectionManager);

                // 尝试创建实例来检查
                if (typeof window.detectionManager === 'function') {
                    const instance = window.detectionManager();
                    console.log('editGroundCalibration 存在:', typeof instance.editGroundCalibration);
                    console.log('可用方法:', Object.getOwnPropertyNames(instance).filter(name => typeof instance[name] === 'function'));
                }
            }, 2000);
        });
    </script>
</body>

</html>