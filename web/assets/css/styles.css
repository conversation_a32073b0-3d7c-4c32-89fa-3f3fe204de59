/* 目标检测管理系统 - 严肃商务风格 */

/* CSS Reset */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html,
body {
    height: 100%;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', 'Helvetica Neue', Arial, sans-serif;
    font-size: 14px;
    line-height: 1.5;
    color: #2c3e50;
    background-color: #f8f9fa;
    padding-bottom: 60px;
    /* 为固定底部留出空间 */
}

/* 布局容器 */
.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* 顶部导航栏 */
.header {
    background: #ffffff;
    border-bottom: 2px solid #e9ecef;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 16px 0;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 12px;
}

.logo {
    width: 40px;
    height: 40px;
    background: #34495e;
    border-radius: 4px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 16px;
}

.title-section h1 {
    font-size: 20px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 2px;
}

.title-section p {
    font-size: 12px;
    color: #7f8c8d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 16px;
}

.connection-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: #f8f9fa;
    border-radius: 4px;
    border: 1px solid #dee2e6;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

.status-dot.connected {
    background: #27ae60;
}

.status-dot.connecting {
    background: #f39c12;
}

.status-dot.disconnected {
    background: #e74c3c;
}

.status-text {
    font-size: 12px;
    color: #6c757d;
    font-weight: 500;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 8px 16px;
    border: 1px solid transparent;
    border-radius: 4px;
    font-size: 13px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.2s ease;
    background: none;
    white-space: nowrap;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
}

.btn-primary {
    background: #3498db;
    color: white;
    border-color: #3498db;
}

.btn-primary:hover:not(:disabled) {
    background: #2980b9;
    border-color: #2980b9;
}

.btn-success {
    background: #27ae60;
    color: white;
    border-color: #27ae60;
}

.btn-success:hover:not(:disabled) {
    background: #229954;
    border-color: #229954;
}

.btn-danger {
    background: #e74c3c;
    color: white;
    border-color: #e74c3c;
}

.btn-danger:hover:not(:disabled) {
    background: #c0392b;
    border-color: #c0392b;
}

.btn-warning {
    background: #f39c12;
    color: white;
    border-color: #f39c12;
}

.btn-warning:hover:not(:disabled) {
    background: #e67e22;
    border-color: #e67e22;
}

.btn-secondary {
    background: #95a5a6;
    color: white;
    border-color: #95a5a6;
}

.btn-secondary:hover:not(:disabled) {
    background: #7f8c8d;
    border-color: #7f8c8d;
}

.btn-outline {
    background: transparent;
    color: #6c757d;
    border-color: #dee2e6;
}

.btn-outline:hover:not(:disabled) {
    background: #f8f9fa;
    color: #495057;
}

/* 系统概览区域 */
.overview-section {
    background: white;
    border-bottom: 1px solid #dee2e6;
    padding: 24px 0;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 24px;
}

.stat-card {
    text-align: center;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.stat-number {
    font-size: 32px;
    font-weight: 700;
    margin-bottom: 4px;
}

.stat-number.success {
    color: #27ae60;
}

.stat-number.danger {
    color: #e74c3c;
}

.stat-number.primary {
    color: #3498db;
}

.stat-label {
    font-size: 13px;
    color: #6c757d;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.action-buttons {
    display: flex;
    flex-direction: column;
    gap: 8px;
    justify-content: center;
}

/* 主要内容区域 */
.main-content {
    padding: 32px 0 80px 0;
    min-height: calc(100vh - 200px);
}

.content-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 32px;
}

.content-actions {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.content-title h2 {
    font-size: 24px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.content-title p {
    color: #7f8c8d;
    font-size: 14px;
}

/* 实例网格 */
.instances-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
    gap: 24px;
}

.instance-card {
    background: white;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 20px;
    transition: all 0.2s ease;
}

.instance-card:hover {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    border-color: #bdc3c7;
}

.instance-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 16px;
}

.instance-info h3 {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 4px;
}

.instance-id {
    font-size: 12px;
    color: #7f8c8d;
    font-family: 'Courier New', monospace;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.status-badge.running {
    background: #d5f4e6;
    color: #27ae60;
    border: 1px solid #a3e9c4;
}

.status-badge.stopped {
    background: #fdeaea;
    color: #e74c3c;
    border: 1px solid #f5b7b1;
}

.instance-details {
    margin-bottom: 20px;
}

.detail-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #f8f9fa;
}

.detail-row:last-child {
    border-bottom: none;
}

.detail-label {
    font-size: 13px;
    color: #7f8c8d;
    font-weight: 500;
}

.detail-value {
    font-size: 13px;
    color: #2c3e50;
    font-weight: 500;
    text-align: right;
    max-width: 60%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.detail-value.input-source {
    max-width: 50%;
    font-family: 'Courier New', monospace;
    font-size: 12px;
}

.instance-actions {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    gap: 6px;
}

.instance-actions .btn {
    font-size: 11px;
    padding: 6px 8px;
    white-space: nowrap;
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 60px 20px;
    color: #7f8c8d;
}

.empty-icon {
    font-size: 48px;
    margin-bottom: 16px;
    opacity: 0.5;
}

.empty-state h3 {
    font-size: 18px;
    color: #95a5a6;
    margin-bottom: 8px;
}

.empty-state p {
    margin-bottom: 24px;
    color: #bdc3c7;
}

/* 底部状态栏 */
.footer {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    background: white;
    border-top: 1px solid #dee2e6;
    padding: 12px 0;
    z-index: 50;
    box-shadow: 0 -2px 4px rgba(0, 0, 0, 0.1);
}

.footer-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 12px;
    color: #7f8c8d;
}

/* 消息提示 */
.message-toast {
    position: fixed;
    bottom: 20px;
    right: 20px;
    max-width: 400px;
    padding: 12px 16px;
    border-radius: 6px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    z-index: 1000;
    display: flex;
    align-items: center;
    gap: 8px;
}

.message-toast.success {
    background: #27ae60;
    color: white;
}

.message-toast.error {
    background: #e74c3c;
    color: white;
}

.message-icon {
    font-weight: bold;
}

/* 模态框 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 20px;
}

.modal {
    background: white;
    border-radius: 6px;
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow: hidden;
}

.modal-header {
    padding: 20px 24px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    color: #2c3e50;
    margin: 0;
    flex: 1;
}

.modal-close {
    background: none;
    border: none;
    font-size: 20px;
    color: #6c757d;
    cursor: pointer;
    padding: 0;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 4px;
    transition: all 0.2s ease;
    flex-shrink: 0;
}

.modal-close:hover {
    background: rgba(0, 0, 0, 0.1);
    color: #495057;
}

.modal-body {
    padding: 24px;
}

.modal-footer {
    padding: 16px 24px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
    display: flex;
    justify-content: flex-end;
    gap: 12px;
}

/* 表单样式 */
.form-group {
    margin-bottom: 20px;
}

.form-label {
    display: block;
    margin-bottom: 6px;
    font-size: 13px;
    font-weight: 600;
    color: #2c3e50;
}

.form-input {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    font-size: 14px;
    transition: border-color 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: #3498db;
    box-shadow: 0 0 0 2px rgba(52, 152, 219, 0.2);
}

.form-help {
    margin-top: 4px;
    font-size: 12px;
    color: #7f8c8d;
}

.form-note {
    padding: 12px;
    background: #e8f4fd;
    border: 1px solid #bee5eb;
    border-radius: 4px;
    font-size: 13px;
    color: #0c5460;
}

.form-warning {
    padding: 12px;
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 4px;
    font-size: 13px;
    color: #856404;
}

/* Alpine.js 过渡动画 */
[x-cloak] {
    display: none !important;
}

/* 区域绘制器样式 */
.region-editor {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.9);
    z-index: 1000;
    display: flex;
    flex-direction: column;
}

.region-editor-header {
    background: #2c3e50;
    color: white;
    padding: 16px 24px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px solid #34495e;
}

.region-editor-title {
    font-size: 18px;
    font-weight: 600;
}

.region-editor-controls {
    display: flex;
    gap: 12px;
    align-items: center;
}

.region-editor-body {
    flex: 1;
    display: flex;
    position: relative;
    overflow: hidden;
}

.region-canvas-container {
    flex: 1;
    position: relative;
    background: #1a1a1a;
    display: flex;
    align-items: center;
    justify-content: center;
    overflow: hidden;
}

#region-canvas-stage {
    transform-origin: center center;
    transition: transform 0.3s ease;
}

/* 日志查看器样式 */
.log-viewer-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.85);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    backdrop-filter: blur(4px);
}

.log-viewer-modal {
    width: 95vw;
    height: 90vh;
    background: #1a202c;
    border-radius: 12px;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.5);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

/* 顶部工具栏 */
.log-toolbar {
    background: linear-gradient(135deg, #2d3748 0%, #4a5568 100%);
    padding: 1rem 1.5rem;
    display: flex;
    align-items: center;
    justify-content: space-between;
    border-bottom: 1px solid #4a5568;
}

.log-toolbar-left {
    flex: 1;
}

.log-title {
    color: white;
    font-size: 1.25rem;
    font-weight: 600;
    margin: 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.log-icon {
    font-size: 1.5rem;
}

.log-toolbar-center {
    flex: 2;
    display: flex;
    justify-content: center;
}

.log-controls {
    display: flex;
    gap: 0.75rem;
    align-items: center;
}

.log-input,
.log-select,
.log-search {
    padding: 0.5rem 0.75rem;
    border: 1px solid #4a5568;
    border-radius: 6px;
    background: #2d3748;
    color: white;
    font-size: 0.875rem;
    transition: all 0.2s ease;
}

.log-input:focus,
.log-select:focus,
.log-search:focus {
    outline: none;
    border-color: #63b3ed;
    box-shadow: 0 0 0 3px rgba(99, 179, 237, 0.1);
}

.log-search {
    min-width: 250px;
}

.log-search::placeholder {
    color: #a0aec0;
}

.log-toolbar-right {
    flex: 1;
    display: flex;
    justify-content: flex-end;
    gap: 0.5rem;
}

.log-btn {
    width: 40px;
    height: 40px;
    border: none;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1.1rem;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.log-btn:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: translateY(-1px);
}

.log-btn-close:hover {
    background: #e53e3e;
}

/* 主内容区 */
.log-main {
    flex: 1;
    display: flex;
    overflow: hidden;
}

/* 左侧统计面板 */
.log-sidebar {
    width: 280px;
    background: #2d3748;
    border-right: 1px solid #4a5568;
    padding: 1.5rem;
    overflow-y: auto;
}

.log-stats-panel h4 {
    color: white;
    font-size: 1rem;
    font-weight: 600;
    margin: 0 0 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.log-stats-panel h4::before {
    content: "📊";
    font-size: 1.2rem;
}

.stat-grid {
    display: grid;
    gap: 0.75rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.05);
    border: 1px solid rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    padding: 1rem;
    text-align: center;
    transition: all 0.2s ease;
}

.stat-card:hover {
    background: rgba(255, 255, 255, 0.08);
    transform: translateY(-1px);
}

.stat-number {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 0.25rem;
}

.stat-label {
    font-size: 0.75rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    opacity: 0.8;
}

.stat-total .stat-number { color: #63b3ed; }
.stat-info .stat-number { color: #68d391; }
.stat-warn .stat-number { color: #fbd38d; }
.stat-error .stat-number { color: #fc8181; }
.stat-debug .stat-number { color: #d6bcfa; }

.stat-total .stat-label { color: #63b3ed; }
.stat-info .stat-label { color: #68d391; }
.stat-warn .stat-label { color: #fbd38d; }
.stat-error .stat-label { color: #fc8181; }
.stat-debug .stat-label { color: #d6bcfa; }

/* 右侧日志内容 */
.log-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    background: #1a202c;
}

.log-loading {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    color: #a0aec0;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid rgba(255, 255, 255, 0.1);
    border-top: 3px solid #63b3ed;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 1rem;
    font-weight: 500;
}

.log-display-wrapper {
    flex: 1;
    overflow: auto;
    background: #0d1117;
}

.log-display {
    margin: 0;
    padding: 1.5rem;
    background: transparent;
    font-family: 'Fira Code', 'SF Mono', 'Monaco', 'Inconsolata', 'Roboto Mono', monospace;
    font-size: 0.875rem;
    line-height: 1.6;
    color: #e6edf3;
    white-space: pre-wrap;
    word-wrap: break-word;
    min-height: 100%;
}

/* 底部状态栏 */
.log-footer {
    background: #2d3748;
    padding: 0.75rem 1.5rem;
    border-top: 1px solid #4a5568;
}

.log-status {
    color: #a0aec0;
    font-size: 0.875rem;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.log-status strong {
    color: white;
    font-weight: 600;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .log-viewer-modal {
        width: 98vw;
        height: 95vh;
    }

    .log-sidebar {
        width: 240px;
    }

    .log-toolbar {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem;
    }

    .log-toolbar-left,
    .log-toolbar-center,
    .log-toolbar-right {
        flex: none;
    }

    .log-controls {
        flex-wrap: wrap;
        gap: 0.5rem;
    }
}

@media (max-width: 768px) {
    .log-main {
        flex-direction: column;
    }

    .log-sidebar {
        width: 100%;
        height: 200px;
        border-right: none;
        border-bottom: 1px solid #4a5568;
    }

    .stat-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 0.5rem;
    }

    .stat-card {
        padding: 0.75rem 0.5rem;
    }

    .stat-number {
        font-size: 1.25rem;
    }

    .log-search {
        min-width: 180px;
    }
}

/* 文本格式日志样式 */
.log-timestamp {
    color: #7c3aed;
    font-weight: 500;
}

.log-level {
    font-weight: 700;
    padding: 0.125rem 0.25rem;
    border-radius: 3px;
    font-size: 0.8rem;
}

.log-level-info {
    color: #22d3ee;
    background: rgba(34, 211, 238, 0.1);
}

.log-level-warn {
    color: #fbbf24;
    background: rgba(251, 191, 36, 0.1);
}

.log-level-error {
    color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
}

.log-level-debug {
    color: #a855f7;
    background: rgba(168, 85, 247, 0.1);
}

.log-module {
    color: #10b981;
    font-weight: 600;
}

.log-message {
    color: #e6edf3;
    margin-left: 0.5rem;
}

.log-raw {
    color: #6b7280;
    font-style: italic;
}

/* 日志行样式 */
.log-display {
    counter-reset: line-number;
}

.log-display .log-line {
    display: block;
    padding: 0.25rem 0;
    border-left: 3px solid transparent;
    padding-left: 0.75rem;
    transition: all 0.2s ease;
}

.log-display .log-line:hover {
    background: rgba(255, 255, 255, 0.02);
    border-left-color: #3b82f6;
}

.log-display .log-line::before {
    counter-increment: line-number;
    content: counter(line-number);
    color: #6b7280;
    font-size: 0.75rem;
    margin-right: 1rem;
    min-width: 3rem;
    display: inline-block;
    text-align: right;
}

.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: #a0aec0;
    font-size: 1rem;
}

.btn-info {
    background: #3182ce;
    color: white;
}

.btn-info:hover {
    background: #2c5aa0;
}

/* 系统配置模态框样式 */
.system-config-modal {
    max-width: 800px;
    width: 90vw;
}

.config-tabs {
    display: flex;
    border-bottom: 1px solid #e2e8f0;
    margin-bottom: 2rem;
}

.config-tab {
    padding: 0.75rem 1.5rem;
    border: none;
    background: none;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    border-bottom: 2px solid transparent;
    transition: all 0.2s ease;
}

.tab-active {
    color: #3182ce;
    border-bottom-color: #3182ce;
    background: rgba(49, 130, 206, 0.05);
}

.tab-inactive {
    color: #718096;
}

.tab-inactive:hover {
    color: #4a5568;
    background: rgba(0, 0, 0, 0.02);
}

.config-panel {
    animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 1rem;
}

.form-checkbox {
    width: 1.25rem;
    height: 1.25rem;
    margin-right: 0.5rem;
    accent-color: #3182ce;
}

.form-help {
    font-size: 0.875rem;
    color: #718096;
    margin-top: 0.25rem;
    margin-bottom: 0;
}

.config-info {
    background: #f7fafc;
    border: 1px solid #e2e8f0;
    border-radius: 8px;
    padding: 1.5rem;
    margin-top: 2rem;
}

.config-info h4 {
    margin: 0 0 1rem 0;
    color: #2d3748;
    font-size: 1rem;
    font-weight: 600;
}

.info-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: white;
    border: 1px solid #e2e8f0;
    border-radius: 6px;
}

.info-label {
    font-weight: 500;
    color: #4a5568;
}

.info-value {
    font-weight: 600;
    color: #2d3748;
}

/* 帮助图标和tooltip */
.help-icon {
    display: inline-block;
    margin-left: 0.5rem;
    width: 1rem;
    height: 1rem;
    font-size: 0.75rem;
    color: #718096;
    cursor: help;
    position: relative;
    transition: color 0.2s ease;
}

.help-icon:hover {
    color: #3182ce;
}

.help-icon[data-tooltip] {
    position: relative;
}

.help-icon[data-tooltip]:hover::before {
    content: attr(data-tooltip);
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: #2d3748;
    color: white;
    padding: 0.75rem 1rem;
    border-radius: 6px;
    font-size: 0.875rem;
    line-height: 1.4;
    white-space: pre-line;
    min-width: 300px;
    max-width: 400px;
    z-index: 10000;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    animation: tooltipFadeIn 0.2s ease;
    pointer-events: none;
}

.help-icon[data-tooltip]:hover::after {
    display: none;
}

@keyframes tooltipFadeIn {
    from {
        opacity: 0;
        transform: translateX(-50%) translateY(5px);
    }
    to {
        opacity: 1;
        transform: translateX(-50%) translateY(0);
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .system-config-modal {
        width: 95vw;
        margin: 1rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .config-tabs {
        flex-direction: column;
    }

    .config-tab {
        text-align: left;
        border-bottom: 1px solid #e2e8f0;
        border-right: none;
    }

    .tab-active {
        border-bottom-color: #e2e8f0;
        border-left: 3px solid #3182ce;
    }
}

.region-sidebar {
    width: 300px;
    background: white;
    border-left: 1px solid #dee2e6;
    display: flex;
    flex-direction: column;
}

.region-sidebar-header {
    padding: 16px;
    border-bottom: 1px solid #dee2e6;
    background: #f8f9fa;
}

.region-sidebar-title {
    font-size: 16px;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 8px;
}

.region-sidebar-body {
    flex: 1;
    padding: 16px;
    overflow-y: auto;
}

.region-list {
    list-style: none;
    margin: 0;
    padding: 0;
}

.region-item {
    padding: 12px;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    margin-bottom: 8px;
    background: white;
    cursor: pointer;
    transition: all 0.2s ease;
}

.region-item:hover {
    border-color: #3498db;
    box-shadow: 0 2px 4px rgba(52, 152, 219, 0.1);
}

.region-item.active {
    border-color: #3498db;
    background: #e8f4fd;
}

.region-item-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.region-item-name {
    font-weight: 600;
    color: #2c3e50;
}

.region-item-id {
    font-size: 12px;
    color: #7f8c8d;
    font-family: 'Courier New', monospace;
}

.region-item-actions {
    display: flex;
    gap: 4px;
}

.region-item-actions .btn {
    font-size: 10px;
    padding: 4px 8px;
}

.region-item-info {
    font-size: 12px;
    color: #7f8c8d;
}

.region-tools {
    padding: 16px;
    border-top: 1px solid #dee2e6;
    background: #f8f9fa;
}

.region-tools-grid {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 6px;
    margin-bottom: 12px;
}

.region-form {
    margin-top: 16px;
    padding-top: 16px;
    border-top: 1px solid #dee2e6;
}

.canvas-overlay {
    position: absolute;
    top: 16px;
    left: 16px;
    background: rgba(0, 0, 0, 0.7);
    color: white;
    padding: 8px 12px;
    border-radius: 4px;
    font-size: 12px;
    z-index: 10;
}

.canvas-instructions {
    position: absolute;
    bottom: 16px;
    left: 16px;
    right: 316px;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px;
    border-radius: 6px;
    font-size: 13px;
    z-index: 10;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.instructions-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.instructions-header h4 {
    margin: 0;
    font-size: 14px;
    color: #3498db;
}

.instructions-close {
    background: none;
    border: none;
    color: #bdc3c7;
    font-size: 16px;
    cursor: pointer;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 3px;
    transition: all 0.2s ease;
}

.instructions-close:hover {
    background: rgba(255, 255, 255, 0.1);
    color: #ecf0f1;
}

.canvas-instructions ul {
    margin: 0;
    padding-left: 16px;
}

.canvas-instructions li {
    margin-bottom: 4px;
}

.show-instructions-btn {
    position: absolute;
    bottom: 16px;
    left: 16px;
    z-index: 10;
}

.show-instructions-btn .btn {
    background: rgba(0, 0, 0, 0.7);
    color: white;
    border: 1px solid rgba(255, 255, 255, 0.2);
    font-size: 12px;
    padding: 6px 12px;
}

.show-instructions-btn .btn:hover {
    background: rgba(0, 0, 0, 0.8);
    border-color: rgba(255, 255, 255, 0.3);
}

/* 删除确认模态框样式 */
.delete-confirmation {
    text-align: center;
    padding: 20px 0;
}

.delete-icon {
    font-size: 48px;
    margin-bottom: 16px;
}

.delete-confirmation p {
    margin: 12px 0;
    font-size: 16px;
    color: #2c3e50;
}

.delete-info {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 6px;
    padding: 12px;
    margin: 16px 0;
    text-align: left;
}

.delete-info div {
    margin: 4px 0;
    font-size: 14px;
}

.delete-warning {
    color: #e74c3c !important;
    font-weight: 600;
    font-size: 14px !important;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 0 16px;
    }

    .header-content {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }

    .header-actions {
        width: 100%;
        justify-content: space-between;
    }

    .stats-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .content-header {
        flex-direction: column;
        gap: 16px;
        align-items: flex-start;
    }

    .instances-grid {
        grid-template-columns: 1fr;
        gap: 16px;
    }

    .modal {
        margin: 20px;
        max-width: none;
    }

    .canvas-instructions {
        right: 16px;
        left: 16px;
        bottom: 80px;
    }

    .show-instructions-btn {
        bottom: 80px;
    }
}