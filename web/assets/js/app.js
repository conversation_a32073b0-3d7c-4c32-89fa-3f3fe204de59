// 目标检测管理系统 - Alpine.js 应用
function detectionManager() {
    console.log('🎯 创建detectionManager实例');

    return {
        // 数据状态
        cameras: [],
        websocket: null,
        connectionStatus: 'connecting',
        lastUpdate: '正在加载...',
        
        // 模态框状态
        showAddModal: false,
        showEditModal: false,
        showDeleteModal: false,
        showRegionEditor: false,
        showEditRegionModal: false,
        showDeleteRegionModal: false,
        showClearCanvasModal: false,
        showGroundCalibrationEditor: false,
        showEditGroundRectangleModal: false,
        showDeleteGroundRectangleModal: false,
        showClearGroundCanvasModal: false,
        showLogViewer: false,
        showSystemConfig: false,
        
        // 表单数据
        newCamera: {
            id: '',
            name: '',
            input: ''
        },

        // 编辑表单数据
        editingCamera: {
            id: '',
            name: '',
            input: '',
            originalId: ''
        },
        
        // 删除目标
        deleteTarget: {
            id: '',
            name: ''
        },

        // 编辑区域数据
        editingRegion: {
            id: '',
            name: '',
            device_id: ''
        },

        // 删除区域数据
        deletingRegion: {
            id: '',
            name: '',
            device_id: ''
        },
        
        // 消息提示
        message: {
            show: false,
            text: '',
            type: 'success'
        },

        // 区域编辑器状态
        editingRegionCamera: null,
        regionList: [],
        selectedRegionId: null,
        isDrawing: false,
        showRegionForm: false,
        newRegionName: '',

        // 平面标定编辑器状态
        editingGroundCalibrationCamera: null,
        groundRectangleList: [],
        selectedGroundRectangleId: null,
        isDrawingGround: false,
        showGroundRectangleForm: false,
        newGroundRectangleName: '',
        newGroundRectangleDescription: '',
        currentGroundPoints: [],
        groundStage: null,
        groundLayer: null,
        actualGroundVideoWidth: null,
        actualGroundVideoHeight: null,
        groundResizeObserver: null,
        showGroundInstructions: true,

        // 编辑地面矩形数据
        editingGroundRectangle: {
            id: '',
            name: '',
            description: ''
        },

        // 删除地面矩形数据
        deletingGroundRectangle: {
            id: '',
            name: ''
        },

        // 日志查看器状态
        logInstanceName: '',
        logInstanceId: '',
        logDate: '',
        logLevel: '',
        logDisplay: '',
        logLoading: false,
        logStats: {
            total: 0,
            info: 0,
            warn: 0,
            error: 0,
            debug: 0
        },
        logSearchTerm: '',
        originalLogDisplay: '',

        // 系统配置相关
        activeConfigTab: 'detection',
        systemConfig: {
            detection: {
                confidence_threshold: 0.3,
                continuous_count: 10
            },
            alarm: {
                alarm_url: '',
                alarm_header: {
                    tenant_key: ''
                },
                cooldown_minute: 20,
                progressive_cooldown: {
                    enabled: true,
                    reset_cooldown_minutes: 5
                }
            },
            log: {
                retention_days: 7
            }
        },
        newRegionDeviceID: '',   // 新增区域的设备ID
        showInstructions: true,  // 默认显示操作说明

        // Konva相关
        stage: null,
        layer: null,
        backgroundImage: null,
        currentPolygon: null,
        currentPoints: [],
        
        // 计算属性
        get totalInstances() {
            return this.cameras.length;
        },
        
        get runningInstances() {
            return this.cameras.filter(c => c.status === 'running').length;
        },
        
        get stoppedInstances() {
            return this.totalInstances - this.runningInstances;
        },
        
        get connectionStatusText() {
            switch (this.connectionStatus) {
                case 'connected': return '实时连接';
                case 'connecting': return '连接中...';
                case 'disconnected': return '连接断开';
                default: return '未知状态';
            }
        },
        
        // 初始化
        async init() {
            console.log('🚀 目标检测管理系统启动');
            this.initWebSocket();
            await this.loadInitialData();

            // 页面卸载时清理WebSocket连接
            window.addEventListener('beforeunload', () => {
                this.cleanup();
            });
        },

        // 清理资源
        cleanup() {
            if (this.websocket) {
                console.log('🧹 清理WebSocket连接');
                this.websocket.close();
                this.websocket = null;
            }
        },
        
        // WebSocket连接
        initWebSocket() {
            // 检查是否已有连接
            if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
                console.log('⚠️ WebSocket已连接，跳过重复创建');
                return;
            }

            // 关闭现有连接（如果存在）
            if (this.websocket) {
                console.log('🔄 关闭现有WebSocket连接');
                this.websocket.close();
            }

            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;

            console.log('🔌 创建新的WebSocket连接:', wsUrl);

            try {
                this.websocket = new WebSocket(wsUrl);
                
                this.websocket.onopen = () => {
                    console.log('✅ WebSocket连接已建立:', wsUrl);
                    this.connectionStatus = 'connected';
                    this.showMessage('实时连接已建立', 'success');
                };
                
                this.websocket.onmessage = (event) => {
                    const message = JSON.parse(event.data);
                    if (message.type === 'status_update') {
                        this.cameras = message.data;
                        this.lastUpdate = '最后更新: ' + new Date().toLocaleTimeString();
                    }
                };
                
                this.websocket.onclose = () => {
                    console.log('❌ WebSocket连接已关闭');
                    this.connectionStatus = 'disconnected';
                    this.showMessage('连接已断开，请刷新页面', 'error');
                };
                
                this.websocket.onerror = (error) => {
                    console.error('WebSocket错误:', error);
                    this.connectionStatus = 'disconnected';
                };
                
            } catch (error) {
                console.error('WebSocket初始化失败:', error);
                this.connectionStatus = 'disconnected';
                this.fallbackToPolling();
            }
        },
        
        // 降级到轮询模式
        fallbackToPolling() {
            console.log('📡 降级到轮询模式');
            this.connectionStatus = 'disconnected';
            this.loadCameras();
            setInterval(() => this.loadCameras(), 10000);
        },
        
        // 加载初始数据
        async loadInitialData() {
            try {
                await this.loadCameras();
                this.lastUpdate = '数据加载完成';
            } catch (error) {
                console.error('加载初始数据失败:', error);
                this.showMessage('加载数据失败: ' + error.message, 'error');
            }
        },
        
        // 加载摄像头列表
        async loadCameras() {
            try {
                const response = await fetch('/api/cameras');
                const result = await response.json();
                
                if (result.success) {
                    this.cameras = result.data;
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                console.error('加载摄像头列表失败:', error);
                throw error;
            }
        },
        
        // 刷新数据
        async refreshData() {
            try {
                await this.loadCameras();
                this.lastUpdate = '最后更新: ' + new Date().toLocaleTimeString();
                this.showMessage('数据刷新成功', 'success');
            } catch (error) {
                this.showMessage('刷新失败: ' + error.message, 'error');
            }
        },
        
        // 切换摄像头状态
        async toggleCamera(cameraId, currentStatus) {
            const action = currentStatus === 'running' ? 'stop' : 'start';
            
            try {
                const response = await fetch(`/api/cameras/${cameraId}/${action}`, {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    this.showMessage(result.message, 'success');
                    // WebSocket会自动更新状态，无需手动刷新
                    if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
                        await this.refreshData();
                    }
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                this.showMessage(`${action === 'start' ? '启动' : '停止'}失败: ` + error.message, 'error');
            }
        },
        
        // 启动所有
        async startAll() {
            try {
                const response = await fetch('/api/cameras/start-all', {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    this.showMessage(result.message, 'success');
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                this.showMessage('批量启动失败: ' + error.message, 'error');
            }
        },
        
        // 停止所有
        async stopAll() {
            try {
                const response = await fetch('/api/cameras/stop-all', {
                    method: 'POST'
                });
                const result = await response.json();
                
                if (result.success) {
                    this.showMessage(result.message, 'success');
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                this.showMessage('批量停止失败: ' + error.message, 'error');
            }
        },
        
        // 新增摄像头
        async addCamera() {
            // 验证必填字段
            if (!this.newCamera.id || !this.newCamera.name || !this.newCamera.input) {
                this.showMessage('请填写所有必填字段', 'error');
                return;
            }
            
            // 检查ID是否已存在
            if (this.cameras.find(c => c.id === this.newCamera.id)) {
                this.showMessage('实例ID已存在，请使用其他ID', 'error');
                return;
            }
            
            try {
                const response = await fetch('/api/cameras', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(this.newCamera)
                });
                const result = await response.json();
                
                if (result.success) {
                    this.showMessage(result.message, 'success');
                    this.showAddModal = false;
                    this.resetNewCamera();
                    
                    // WebSocket会自动更新状态，无需手动刷新
                    if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
                        await this.refreshData();
                    }
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                this.showMessage('添加检测实例失败: ' + error.message, 'error');
            }
        },
        
        // 删除摄像头
        deleteCamera(cameraId, cameraName) {
            this.deleteTarget = { id: cameraId, name: cameraName };
            this.showDeleteModal = true;
        },
        
        // 确认删除
        async confirmDelete() {
            try {
                const response = await fetch(`/api/cameras/${this.deleteTarget.id}`, {
                    method: 'DELETE'
                });
                const result = await response.json();
                
                if (result.success) {
                    this.showMessage(result.message, 'success');
                    this.showDeleteModal = false;
                    
                    // WebSocket会自动更新状态，无需手动刷新
                    if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
                        await this.refreshData();
                    }
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                this.showMessage('删除检测实例失败: ' + error.message, 'error');
            }
        },
        
        // 编辑实例
        editCamera(cameraId) {
            const camera = this.cameras.find(c => c.id === cameraId);
            if (!camera) {
                this.showMessage('找不到指定的检测实例', 'error');
                return;
            }

            this.editingCamera = {
                id: camera.id,
                name: camera.name,
                input: camera.input,
                originalId: camera.id
            };
            this.showEditModal = true;
        },

        // 保存修改
        async saveEditCamera() {
            // 验证必填字段
            if (!this.editingCamera.id || !this.editingCamera.name || !this.editingCamera.input) {
                this.showMessage('请填写所有必填字段', 'error');
                return;
            }

            // 如果ID发生变化，检查新ID是否已存在
            if (this.editingCamera.id !== this.editingCamera.originalId) {
                if (this.cameras.find(c => c.id === this.editingCamera.id)) {
                    this.showMessage('实例ID已存在，请使用其他ID', 'error');
                    return;
                }
            }

            try {
                const response = await fetch(`/api/cameras/${this.editingCamera.originalId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        id: this.editingCamera.id,
                        name: this.editingCamera.name,
                        input: this.editingCamera.input
                    })
                });
                const result = await response.json();

                if (result.success) {
                    this.showMessage(result.message, 'success');
                    this.showEditModal = false;
                    this.resetEditCamera();

                    // WebSocket会自动更新状态，无需手动刷新
                    if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
                        await this.refreshData();
                    }
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                this.showMessage('修改检测实例失败: ' + error.message, 'error');
            }
        },

        // 编辑区域
        async editRegions(cameraId) {
            const camera = this.cameras.find(c => c.id === cameraId);
            if (!camera) {
                this.showMessage('找不到指定的检测实例', 'error');
                return;
            }

            this.editingRegionCamera = camera;
            this.regionList = camera.regions || [];
            this.selectedRegionId = null;
            this.showRegionEditor = true;

            // 显示加载提示
            this.showMessage('正在获取视频帧...', 'success');

            // 延迟初始化Konva，确保DOM已渲染
            setTimeout(async () => {
                await this.initKonvaStage();
            }, 100);
        },
        
        // 重置新增表单
        resetNewCamera() {
            this.newCamera = {
                id: '',
                name: '',
                input: ''
            };
        },

        // 重置编辑表单
        resetEditCamera() {
            this.editingCamera = {
                id: '',
                name: '',
                input: '',
                originalId: ''
            };
        },

        // 编辑平面标定
        async editGroundCalibration(cameraId) {
            const camera = this.cameras.find(c => c.id === cameraId);
            if (!camera) {
                this.showMessage('找不到指定的检测实例', 'error');
                return;
            }

            this.editingGroundCalibrationCamera = camera;
            this.groundRectangleList = camera.ground_calibration?.rectangles || [];
            this.selectedGroundRectangleId = null;
            this.showGroundCalibrationEditor = true;

            // 显示加载提示
            this.showMessage('正在获取视频帧...', 'success');

            // 延迟初始化Konva，确保DOM已渲染
            setTimeout(async () => {
                await this.initGroundKonvaStage();
            }, 100);
        },

        // 初始化平面标定Konva舞台
        async initGroundKonvaStage() {
            const container = document.getElementById('ground-calibration-canvas-stage');
            if (!container) {
                console.error('找不到平面标定画布容器');
                return;
            }

            // 清理现有舞台
            if (this.groundStage) {
                this.groundStage.destroy();
                this.groundStage = null;
                this.groundLayer = null;
            }

            // 创建临时舞台（将在加载视频帧后调整尺寸）
            this.groundStage = new Konva.Stage({
                container: 'ground-calibration-canvas-stage',
                width: 800,  // 临时尺寸
                height: 600  // 临时尺寸
            });

            // 创建图层
            this.groundLayer = new Konva.Layer();
            this.groundStage.add(this.groundLayer);

            // 先添加临时背景
            this.addGroundTemporaryBackground();

            // 尝试加载视频帧
            try {
                await this.loadGroundVideoFrame();
            } catch (error) {
                console.error('加载视频帧失败:', error);
                this.showMessage('获取视频帧失败: ' + error.message, 'error');
                this.addGroundErrorBackground();
            }

            // 绑定事件
            this.bindGroundStageEvents();

            // 渲染现有矩形
            this.renderExistingGroundRectangles();

            // 添加窗口大小改变监听器
            this.groundResizeObserver = new ResizeObserver(() => {
                if (this.actualGroundVideoWidth && this.actualGroundVideoHeight) {
                    this.applyGroundCanvasAutoScale();
                }
            });

            const containerElement = document.querySelector('.region-canvas-container');
            if (containerElement) {
                this.groundResizeObserver.observe(containerElement);
            }

            console.log('✅ 平面标定Konva舞台初始化完成');
        },

        // 获取平面标定视频帧
        async loadGroundVideoFrame() {
            if (!this.editingGroundCalibrationCamera || !this.editingGroundCalibrationCamera.input) {
                throw new Error('没有有效的视频输入源');
            }

            const response = await fetch('/api/frame', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    rtsp_url: this.editingGroundCalibrationCamera.input
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const blob = await response.blob();
            const imageUrl = URL.createObjectURL(blob);

            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => {
                    this.addGroundVideoBackground(img, imageUrl);
                    resolve();
                };
                img.onerror = () => {
                    URL.revokeObjectURL(imageUrl);
                    reject(new Error('图片加载失败'));
                };
                img.src = imageUrl;
            });
        },

        // 添加平面标定视频帧背景
        addGroundVideoBackground(img, imageUrl) {
            // 清除现有背景
            this.groundLayer.destroyChildren();

            // 获取实际图像尺寸
            const imgWidth = img.naturalWidth;
            const imgHeight = img.naturalHeight;

            // 调整舞台尺寸为实际视频尺寸（1:1显示）
            this.groundStage.width(imgWidth);
            this.groundStage.height(imgHeight);

            // 创建Konva图像对象（1:1显示，无缩放）
            const konvaImage = new Konva.Image({
                x: 0,
                y: 0,
                image: img,
                width: imgWidth,
                height: imgHeight,
                name: 'background-image'
            });

            this.groundLayer.add(konvaImage);

            // 添加半透明遮罩以便更好地显示矩形
            const overlay = new Konva.Rect({
                x: 0,
                y: 0,
                width: imgWidth,
                height: imgHeight,
                fill: 'rgba(0, 0, 0, 0.1)',
                name: 'overlay'
            });

            this.groundLayer.add(overlay);
            this.groundLayer.draw();

            // 保存实际视频尺寸
            this.actualGroundVideoWidth = imgWidth;
            this.actualGroundVideoHeight = imgHeight;

            // 应用自适应缩放
            this.applyGroundCanvasAutoScale();

            // 清理URL对象
            URL.revokeObjectURL(imageUrl);

            this.showMessage(`视频帧加载成功 (${imgWidth}x${imgHeight})`, 'success');
            console.log(`✅ 平面标定Canvas已调整为实际视频尺寸: ${imgWidth}x${imgHeight}`);
        },

        // 应用平面标定canvas自适应缩放
        applyGroundCanvasAutoScale() {
            if (!this.groundStage || !this.actualGroundVideoWidth || !this.actualGroundVideoHeight) {
                return;
            }

            const container = document.querySelector('.region-canvas-container');
            const canvasElement = document.getElementById('ground-calibration-canvas-stage');

            if (!container || !canvasElement) {
                return;
            }

            // 获取容器可用空间
            const availableWidth = container.clientWidth - 40; // 减去padding
            const availableHeight = container.clientHeight - 40;

            // 计算缩放比例
            const scaleX = availableWidth / this.actualGroundVideoWidth;
            const scaleY = availableHeight / this.actualGroundVideoHeight;
            const scale = Math.min(scaleX, scaleY);

            // 应用CSS变换
            canvasElement.style.transform = `scale(${scale})`;

            console.log(`✅ 平面标定Canvas自适应缩放: ${scale.toFixed(3)}x (容器: ${availableWidth}x${availableHeight}, 视频: ${this.actualGroundVideoWidth}x${this.actualGroundVideoHeight})`);
        },

        // 添加平面标定临时背景（加载中）
        addGroundTemporaryBackground() {
            const background = new Konva.Rect({
                x: 0,
                y: 0,
                width: this.groundStage.width(),
                height: this.groundStage.height(),
                fill: '#2c3e50',
                name: 'temp-background'
            });

            this.groundLayer.add(background);

            const text = new Konva.Text({
                x: this.groundStage.width() / 2,
                y: this.groundStage.height() / 2,
                text: '正在获取视频帧...',
                fontSize: 18,
                fontFamily: 'Arial',
                fill: '#ecf0f1',
                align: 'center',
                offsetX: 80,
                offsetY: 9,
                name: 'temp-text'
            });

            this.groundLayer.add(text);
            this.groundLayer.draw();
        },

        // 添加平面标定错误背景
        addGroundErrorBackground() {
            // 清除现有背景
            this.groundLayer.destroyChildren();

            const background = new Konva.Rect({
                x: 0,
                y: 0,
                width: this.groundStage.width(),
                height: this.groundStage.height(),
                fill: '#34495e',
                name: 'error-background'
            });

            this.groundLayer.add(background);

            // 添加网格
            this.addGroundGrid();

            const text = new Konva.Text({
                x: this.groundStage.width() / 2,
                y: this.groundStage.height() / 2,
                text: '无法获取视频帧\n请检查输入源是否正常\n\n点击"新增矩形"开始绘制平面标定矩形',
                fontSize: 16,
                fontFamily: 'Arial',
                fill: '#ecf0f1',
                align: 'center',
                offsetX: 150,
                offsetY: 40,
                name: 'error-text'
            });

            this.groundLayer.add(text);
            this.groundLayer.draw();
        },

        // 添加平面标定网格
        addGroundGrid() {
            const width = this.groundStage.width();
            const height = this.groundStage.height();
            const gridSize = 50;

            // 垂直线
            for (let i = 0; i <= width / gridSize; i++) {
                const line = new Konva.Line({
                    points: [i * gridSize, 0, i * gridSize, height],
                    stroke: '#34495e',
                    strokeWidth: 0.5,
                    name: 'grid-line'
                });
                this.groundLayer.add(line);
            }

            // 水平线
            for (let i = 0; i <= height / gridSize; i++) {
                const line = new Konva.Line({
                    points: [0, i * gridSize, width, i * gridSize],
                    stroke: '#34495e',
                    strokeWidth: 0.5,
                    name: 'grid-line'
                });
                this.groundLayer.add(line);
            }
        },

        // 绑定平面标定舞台事件
        bindGroundStageEvents() {
            this.groundStage.on('click', (e) => {
                // 如果点击的是背景或网格，且正在绘制中
                if (this.isDrawingGround && (e.target === this.groundStage || e.target.name().includes('background') || e.target.name().includes('overlay') || e.target.name().includes('grid'))) {
                    const pos = this.groundStage.getPointerPosition();
                    this.addGroundPoint(pos.x, pos.y);
                }
            });
        },

        // 渲染现有地面矩形
        renderExistingGroundRectangles() {
            this.groundRectangleList.forEach(rectangle => {
                this.drawGroundRectangle(rectangle);
            });
        },

        // 显示消息
        showMessage(text, type = 'success') {
            this.message = {
                show: true,
                text: text,
                type: type
            };

            // 3秒后自动隐藏
            setTimeout(() => {
                this.message.show = false;
            }, 3000);
        },

        // 绘制地面矩形
        drawGroundRectangle(rectangle) {
            const points = rectangle.points.flat();

            // 创建矩形
            const rect = new Konva.Line({
                points: points,
                fill: 'rgba(0, 210, 211, 0.3)',  // 青色填充
                stroke: '#00d2d3',               // 青色边框
                strokeWidth: 3,
                closed: true,
                draggable: false,
                name: `ground-rect-${rectangle.id}`
            });

            // 添加点击事件
            rect.on('click', () => {
                this.selectGroundRectangle(rectangle.id);
            });

            this.groundLayer.add(rect);

            // 添加顶点
            rectangle.points.forEach((point, index) => {
                this.addGroundVertex(point[0], point[1], rectangle.id, index);
            });

            // 添加标签
            this.addGroundRectangleLabel(rectangle);

            this.groundLayer.draw();
        },

        // 添加地面矩形顶点
        addGroundVertex(x, y, rectangleId, index) {
            const vertex = new Konva.Circle({
                x: x,
                y: y,
                radius: 8,
                fill: '#00d2d3',              // 青色填充
                stroke: '#ffffff',            // 白色边框
                strokeWidth: 2,
                draggable: true,
                name: `ground-vertex-${rectangleId}-${index}`
            });

            // 拖拽事件
            vertex.on('dragmove', () => {
                this.updateGroundRectangleFromVertex(rectangleId, index, vertex.x(), vertex.y());
            });

            this.groundLayer.add(vertex);
        },

        // 添加地面矩形标签
        addGroundRectangleLabel(rectangle) {
            const points = rectangle.points;
            if (points.length === 0) return;

            // 计算中心点
            const centerX = points.reduce((sum, p) => sum + p[0], 0) / points.length;
            const centerY = points.reduce((sum, p) => sum + p[1], 0) / points.length;

            // 构建标签文本
            const labelText = rectangle.name;

            const label = new Konva.Text({
                x: centerX,
                y: centerY,
                text: labelText,
                fontSize: 12,
                fontFamily: 'Arial',
                fill: '#2c3e50',
                align: 'center',
                offsetX: 50,
                offsetY: 12,
                name: `ground-label-${rectangle.id}`
            });

            // 背景
            const labelBg = new Konva.Rect({
                x: centerX - 55,
                y: centerY - 14,
                width: 110,
                height: 28,
                fill: 'rgba(255, 255, 255, 0.9)',
                cornerRadius: 3,
                name: `ground-label-bg-${rectangle.id}`
            });

            this.groundLayer.add(labelBg);
            this.groundLayer.add(label);
        },

        // 开始绘制地面矩形
        startDrawingGroundRectangle() {
            if (this.isDrawingGround) return;

            this.isDrawingGround = true;
            this.currentGroundPoints = [];
            this.showMessage('请按顺序点击四个角点绘制矩形', 'info');
        },

        // 添加地面矩形点
        addGroundPoint(x, y) {
            if (!this.isDrawingGround) return;

            this.currentGroundPoints.push([x, y]);

            // 绘制临时点
            const isFirstPoint = this.currentGroundPoints.length === 1;
            const point = new Konva.Circle({
                x: x,
                y: y,
                radius: isFirstPoint ? 10 : 6,
                fill: isFirstPoint ? '#e74c3c' : '#00d2d3',
                stroke: '#ffffff',
                strokeWidth: 2,
                name: 'temp-ground-point'
            });

            this.groundLayer.add(point);

            // 如果是第一个点，添加提示文字
            if (isFirstPoint) {
                const text = new Konva.Text({
                    x: x + 15,
                    y: y - 5,
                    text: '起点',
                    fontSize: 12,
                    fontFamily: 'Arial',
                    fill: '#e74c3c',
                    name: 'temp-ground-text'
                });
                this.groundLayer.add(text);
            }

            // 如果有多个点，绘制连线
            if (this.currentGroundPoints.length > 1) {
                this.drawGroundTempLines();
            }

            // 如果已经有4个点，完成矩形绘制
            if (this.currentGroundPoints.length === 4) {
                this.finishGroundRectangleDrawing();
            }

            this.groundLayer.draw();
        },

        // 绘制地面矩形临时连线
        drawGroundTempLines() {
            // 清除之前的临时线条
            this.groundStage.find('.temp-ground-line').forEach(node => node.destroy());

            const points = this.currentGroundPoints.flat();

            const line = new Konva.Line({
                points: points,
                stroke: '#00d2d3',
                strokeWidth: 2,
                name: 'temp-ground-line'
            });

            this.groundLayer.add(line);
        },

        // 完成地面矩形绘制
        finishGroundRectangleDrawing() {
            if (this.currentGroundPoints.length !== 4) {
                this.showMessage('矩形需要4个点', 'error');
                return;
            }

            this.isDrawingGround = false;
            this.showGroundRectangleForm = true;
            this.newGroundRectangleName = `地面矩形${this.groundRectangleList.length + 1}`;

            // 清理临时元素
            this.clearGroundTempElements();

            this.showMessage('请输入矩形的真实尺寸信息', 'info');
        },

        // 清理平面标定临时元素
        clearGroundTempElements() {
            this.groundStage.find(node => {
                const name = node.name();
                return name.startsWith('temp-ground-');
            }).forEach(node => node.destroy());

            this.groundLayer.draw();
        },

        // 初始化Konva舞台
        async initKonvaStage() {
            const container = document.getElementById('region-canvas-stage');
            if (!container) {
                console.error('找不到画布容器');
                return;
            }

            // 清理现有舞台
            if (this.stage) {
                this.stage.destroy();
            }

            // 创建临时舞台（将在加载视频帧后调整尺寸）
            this.stage = new Konva.Stage({
                container: 'region-canvas-stage',
                width: 800,  // 临时尺寸
                height: 600  // 临时尺寸
            });

            // 创建图层
            this.layer = new Konva.Layer();
            this.stage.add(this.layer);

            // 先添加临时背景
            this.addTemporaryBackground();

            // 获取并加载视频帧作为背景
            try {
                await this.loadVideoFrame();
            } catch (error) {
                console.error('加载视频帧失败:', error);
                this.showMessage('获取视频帧失败，使用默认背景', 'error');
                // 如果获取视频帧失败，使用默认背景
                this.addDefaultBackground();
            }

            // 绑定事件
            this.bindStageEvents();

            // 渲染现有区域
            this.renderExistingRegions();

            // 添加窗口大小改变监听器
            this.resizeObserver = new ResizeObserver(() => {
                if (this.actualVideoWidth && this.actualVideoHeight) {
                    this.applyCanvasAutoScale();
                }
            });

            const containerElement = document.querySelector('.region-canvas-container');
            if (containerElement) {
                this.resizeObserver.observe(containerElement);
            }

            console.log('✅ Konva舞台初始化完成');
        },

        // 获取视频帧
        async loadVideoFrame() {
            if (!this.editingRegionCamera || !this.editingRegionCamera.input) {
                throw new Error('没有有效的视频输入源');
            }

            const response = await fetch('/api/frame', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    rtsp_url: this.editingRegionCamera.input
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const blob = await response.blob();
            const imageUrl = URL.createObjectURL(blob);

            return new Promise((resolve, reject) => {
                const img = new Image();
                img.onload = () => {
                    this.addVideoBackground(img, imageUrl);
                    resolve();
                };
                img.onerror = () => {
                    URL.revokeObjectURL(imageUrl);
                    reject(new Error('图片加载失败'));
                };
                img.src = imageUrl;
            });
        },

        // 添加视频帧背景
        addVideoBackground(img, imageUrl) {
            // 清除现有背景
            this.layer.destroyChildren();

            // 获取实际视频尺寸
            const imgWidth = img.width;
            const imgHeight = img.height;

            // 设置stage为实际视频尺寸
            this.stage.width(imgWidth);
            this.stage.height(imgHeight);

            // 创建Konva图像对象（1:1显示，无缩放）
            const konvaImage = new Konva.Image({
                x: 0,
                y: 0,
                image: img,
                width: imgWidth,
                height: imgHeight,
                name: 'background-image'
            });

            this.layer.add(konvaImage);

            // 添加半透明遮罩以便更好地显示区域
            const overlay = new Konva.Rect({
                x: 0,
                y: 0,
                width: imgWidth,
                height: imgHeight,
                fill: 'rgba(0, 0, 0, 0.2)',
                name: 'background-overlay'
            });

            this.layer.add(overlay);
            this.layer.draw();

            // 清理URL对象
            setTimeout(() => {
                URL.revokeObjectURL(imageUrl);
            }, 1000);

            // 保存实际视频尺寸，用于后续使用
            this.actualVideoWidth = imgWidth;
            this.actualVideoHeight = imgHeight;

            // 应用自适应缩放
            this.applyCanvasAutoScale();

            this.showMessage(`视频帧加载成功 (${imgWidth}x${imgHeight})`, 'success');
            console.log(`✅ Canvas已调整为实际视频尺寸: ${imgWidth}x${imgHeight}`);
        },

        // 应用canvas自适应缩放
        applyCanvasAutoScale() {
            if (!this.stage || !this.actualVideoWidth || !this.actualVideoHeight) {
                return;
            }

            const container = document.querySelector('.region-canvas-container');
            const canvasElement = document.getElementById('region-canvas-stage');

            if (!container || !canvasElement) {
                return;
            }

            // 获取容器的可用空间（减去一些padding）
            const containerRect = container.getBoundingClientRect();
            const availableWidth = containerRect.width - 32; // 减去padding
            const availableHeight = containerRect.height - 32;

            // 计算缩放比例（保持长宽比）
            const scaleX = availableWidth / this.actualVideoWidth;
            const scaleY = availableHeight / this.actualVideoHeight;
            const scale = Math.min(scaleX, scaleY);

            // 应用CSS变换
            canvasElement.style.transform = `scale(${scale})`;

            console.log(`✅ Canvas自适应缩放: ${scale.toFixed(3)}x (容器: ${availableWidth}x${availableHeight}, 视频: ${this.actualVideoWidth}x${this.actualVideoHeight})`);
        },

        // 添加临时背景（加载中）
        addTemporaryBackground() {
            const background = new Konva.Rect({
                x: 0,
                y: 0,
                width: this.stage.width(),
                height: this.stage.height(),
                fill: '#2c3e50',
                stroke: '#34495e',
                strokeWidth: 2,
                name: 'temp-background'
            });

            this.layer.add(background);

            const text = new Konva.Text({
                x: this.stage.width() / 2,
                y: this.stage.height() / 2,
                text: '正在获取视频帧...',
                fontSize: 16,
                fontFamily: 'Arial',
                fill: '#95a5a6',
                align: 'center',
                offsetX: 60,
                offsetY: 8,
                name: 'temp-text'
            });

            this.layer.add(text);
            this.layer.draw();
        },

        // 添加默认背景（获取视频帧失败时使用）
        addDefaultBackground() {
            // 清除现有背景
            this.layer.destroyChildren();

            const background = new Konva.Rect({
                x: 0,
                y: 0,
                width: this.stage.width(),
                height: this.stage.height(),
                fill: '#2c3e50',
                stroke: '#34495e',
                strokeWidth: 2,
                name: 'default-background'
            });

            this.layer.add(background);

            // 添加网格
            this.addGrid();

            const text = new Konva.Text({
                x: this.stage.width() / 2,
                y: this.stage.height() / 2,
                text: '无法获取视频帧\n请检查输入源是否正常\n\n点击"新增区域"开始绘制检测区域',
                fontSize: 16,
                fontFamily: 'Arial',
                fill: '#95a5a6',
                align: 'center',
                offsetX: 120,
                offsetY: 30,
                name: 'default-text'
            });

            this.layer.add(text);
            this.layer.draw();
        },

        // 添加网格
        addGrid() {
            const gridSize = 20;
            const width = this.stage.width();
            const height = this.stage.height();

            // 垂直线
            for (let i = 0; i <= width / gridSize; i++) {
                const line = new Konva.Line({
                    points: [i * gridSize, 0, i * gridSize, height],
                    stroke: '#34495e',
                    strokeWidth: 0.5,
                    opacity: 0.3
                });
                this.layer.add(line);
            }

            // 水平线
            for (let i = 0; i <= height / gridSize; i++) {
                const line = new Konva.Line({
                    points: [0, i * gridSize, width, i * gridSize],
                    stroke: '#34495e',
                    strokeWidth: 0.5,
                    opacity: 0.3
                });
                this.layer.add(line);
            }
        },

        // 绑定舞台事件
        bindStageEvents() {
            this.stage.on('click', (e) => {
                if (this.isDrawing) {
                    const clickX = e.evt.offsetX;
                    const clickY = e.evt.offsetY;

                    // 检查是否点击了第一个点（完成绘制）
                    if (this.currentPoints.length >= 3) {
                        const firstPoint = this.currentPoints[0];
                        const distance = Math.sqrt(
                            Math.pow(clickX - firstPoint[0], 2) +
                            Math.pow(clickY - firstPoint[1], 2)
                        );

                        // 如果点击距离第一个点很近（15像素内），完成绘制
                        if (distance <= 15) {
                            this.finishDrawing();
                            return;
                        }
                    }

                    // 否则添加新点
                    this.addPoint(clickX, clickY);
                }
            });

            // 右键取消绘制
            this.stage.on('contextmenu', (e) => {
                e.evt.preventDefault(); // 阻止浏览器右键菜单
                if (this.isDrawing) {
                    this.cancelDrawing();
                }
            });

            // 移除双击事件，改用点击原点完成
            // this.stage.on('dblclick', () => {
            //     if (this.isDrawing && this.currentPoints.length >= 3) {
            //         this.finishDrawing();
            //     }
            // });
        },

        // 渲染现有区域
        renderExistingRegions() {
            this.regionList.forEach(region => {
                this.drawRegion(region);
            });
        },

        // 绘制区域
        drawRegion(region) {
            const points = region.points.flat();

            // 创建多边形
            const polygon = new Konva.Line({
                points: points,
                fill: 'rgba(255, 193, 7, 0.4)',  // 更明显的黄色填充
                stroke: '#ffc107',               // 黄色边框
                strokeWidth: 3,                  // 更粗的边框
                closed: true,
                draggable: false,
                name: `region-${region.id}`
            });

            // 添加点击事件
            polygon.on('click', () => {
                this.selectRegion(region.id);
            });

            this.layer.add(polygon);

            // 添加顶点
            region.points.forEach((point, index) => {
                this.addVertex(point[0], point[1], region.id, index);
            });

            // 添加标签
            this.addRegionLabel(region);

            this.layer.draw();
        },

        // 添加顶点
        addVertex(x, y, regionId, index) {
            const vertex = new Konva.Circle({
                x: x,
                y: y,
                radius: 8,                    // 更大的顶点
                fill: '#ff4757',              // 更明显的红色
                stroke: '#ffffff',            // 白色边框
                strokeWidth: 2,
                draggable: true,
                name: `vertex-${regionId}-${index}`
            });

            // 拖拽事件
            vertex.on('dragmove', () => {
                this.updateRegionFromVertex(regionId, index, vertex.x(), vertex.y());
            });

            this.layer.add(vertex);
        },

        // 添加区域标签
        addRegionLabel(region) {
            const points = region.points;
            if (points.length === 0) return;

            // 计算中心点
            const centerX = points.reduce((sum, p) => sum + p[0], 0) / points.length;
            const centerY = points.reduce((sum, p) => sum + p[1], 0) / points.length;

            // 构建标签文本
            const labelText = region.device_id ?
                `${region.name}\n[${region.device_id}]` :
                region.name;

            const label = new Konva.Text({
                x: centerX,
                y: centerY,
                text: labelText,
                fontSize: 12,
                fontFamily: 'Arial',
                fill: '#2c3e50',
                align: 'center',
                offsetX: 40,
                offsetY: region.device_id ? 12 : 6,
                name: `label-${region.id}`
            });

            // 背景（根据是否有设备ID调整大小）
            const bgHeight = region.device_id ? 28 : 18;
            const labelBg = new Konva.Rect({
                x: centerX - 45,
                y: centerY - (bgHeight / 2),
                width: 90,
                height: bgHeight,
                fill: 'rgba(255, 255, 255, 0.9)',
                cornerRadius: 3,
                name: `label-bg-${region.id}`
            });

            this.layer.add(labelBg);
            this.layer.add(label);
        },

        // 开始绘制
        startDrawing() {
            this.isDrawing = true;
            this.currentPoints = [];
            this.currentPolygon = null;
            this.showMessage('开始绘制区域，点击画布添加顶点，双击完成', 'success');
        },

        // 添加点
        addPoint(x, y) {
            this.currentPoints.push([x, y]);

            // 绘制临时点
            const isFirstPoint = this.currentPoints.length === 1;
            const point = new Konva.Circle({
                x: x,
                y: y,
                radius: isFirstPoint ? 10 : 6,  // 第一个点更大
                fill: isFirstPoint ? '#00d2d3' : '#ff4757',  // 第一个点用青色
                stroke: '#ffffff',
                strokeWidth: isFirstPoint ? 3 : 2,  // 第一个点边框更粗
                name: 'temp-point'
            });

            this.layer.add(point);

            // 如果是第一个点，添加提示文字
            if (isFirstPoint) {
                const text = new Konva.Text({
                    x: x + 15,
                    y: y - 5,
                    text: '起点',
                    fontSize: 12,
                    fontFamily: 'Arial',
                    fill: '#00d2d3',
                    name: 'temp-point'
                });
                this.layer.add(text);
            }

            // 如果有多个点，绘制临时线条
            if (this.currentPoints.length > 1) {
                this.updateTempPolygon();
            }

            // 如果有3个或更多点，显示完成提示
            if (this.currentPoints.length >= 3) {
                this.showFinishHint();
            }

            this.layer.draw();
        },

        // 更新临时多边形
        updateTempPolygon() {
            // 移除旧的临时多边形
            const oldPolygon = this.stage.findOne('.temp-polygon');
            if (oldPolygon) {
                oldPolygon.destroy();
            }

            const points = this.currentPoints.flat();

            const polygon = new Konva.Line({
                points: points,
                stroke: '#00d2d3',              // 青色边框
                strokeWidth: 3,
                fill: 'rgba(0, 210, 211, 0.3)', // 青色半透明填充
                closed: false,
                name: 'temp-polygon'
            });

            this.layer.add(polygon);
            this.layer.draw();
        },

        // 显示完成提示
        showFinishHint() {
            // 移除旧的提示
            this.stage.find('.finish-hint').forEach(node => node.destroy());

            const firstPoint = this.currentPoints[0];
            const hintText = new Konva.Text({
                x: firstPoint[0] + 15,
                y: firstPoint[1] + 15,
                text: '点击起点完成绘制',
                fontSize: 12,
                fontFamily: 'Arial',
                fill: '#00d2d3',
                name: 'finish-hint'
            });

            // 添加背景
            const hintBg = new Konva.Rect({
                x: firstPoint[0] + 12,
                y: firstPoint[1] + 12,
                width: 100,
                height: 18,
                fill: 'rgba(0, 0, 0, 0.7)',
                cornerRadius: 3,
                name: 'finish-hint'
            });

            this.layer.add(hintBg);
            this.layer.add(hintText);
        },

        // 完成绘制
        finishDrawing() {
            if (this.currentPoints.length < 3) {
                this.showMessage('至少需要3个点才能形成区域', 'error');
                return;
            }

            this.isDrawing = false;
            this.showRegionForm = true;
            this.newRegionName = `区域${this.regionList.length + 1}`;

            // 清理临时元素
            this.clearTempElements();
        },

        // 清理临时元素
        clearTempElements() {
            this.stage.find('.temp-point').forEach(node => node.destroy());
            this.stage.find('.temp-polygon').forEach(node => node.destroy());
            this.stage.find('.finish-hint').forEach(node => node.destroy());
            this.layer.draw();
        },

        // 确认新地面矩形
        confirmNewGroundRectangle() {
            if (!this.newGroundRectangleName.trim()) {
                this.showMessage('请输入矩形名称', 'error');
                return;
            }

            const newRectangle = {
                id: `ground_rect_${Date.now()}`,
                name: this.newGroundRectangleName.trim(),
                points: [...this.currentGroundPoints],
                description: this.newGroundRectangleDescription.trim() || ''
            };

            this.groundRectangleList.push(newRectangle);
            this.drawGroundRectangle(newRectangle);

            // 重置状态
            this.showGroundRectangleForm = false;
            this.newGroundRectangleName = '';
            this.newGroundRectangleDescription = '';
            this.currentGroundPoints = [];

            this.showMessage(`地面矩形 "${newRectangle.name}" 添加成功`, 'success');
        },

        // 取消新地面矩形
        cancelNewGroundRectangle() {
            this.showGroundRectangleForm = false;
            this.newGroundRectangleName = '';
            this.newGroundRectangleDescription = '';
            this.currentGroundPoints = [];
            this.clearGroundTempElements();
        },

        // 选择地面矩形
        selectGroundRectangle(rectangleId) {
            this.selectedGroundRectangleId = rectangleId;
            this.highlightGroundRectangle(rectangleId);
        },

        // 高亮地面矩形
        highlightGroundRectangle(rectangleId) {
            // 重置所有矩形样式
            this.groundStage.find('Line').forEach(line => {
                if (line.name().startsWith('ground-rect-')) {
                    line.stroke('#00d2d3');     // 默认青色
                    line.strokeWidth(3);
                }
            });

            // 高亮选中矩形
            const selectedRectangle = this.groundStage.findOne(`.ground-rect-${rectangleId}`);
            if (selectedRectangle) {
                selectedRectangle.stroke('#ff4757');  // 选中时红色
                selectedRectangle.strokeWidth(4);     // 更粗的边框
            }

            this.groundLayer.draw();
        },

        // 更新地面矩形顶点
        updateGroundRectangleFromVertex(rectangleId, vertexIndex, newX, newY) {
            const rectangle = this.groundRectangleList.find(r => r.id === rectangleId);
            if (rectangle && rectangle.points[vertexIndex]) {
                rectangle.points[vertexIndex] = [newX, newY];

                // 更新矩形
                const rect = this.groundStage.findOne(`.ground-rect-${rectangleId}`);
                if (rect) {
                    rect.points(rectangle.points.flat());
                }

                // 更新标签位置
                this.updateGroundRectangleLabel(rectangle);

                this.groundLayer.draw();
            }
        },

        // 更新地面矩形标签
        updateGroundRectangleLabel(rectangle) {
            // 移除旧标签
            this.groundStage.find(node => {
                const name = node.name();
                return name === `ground-label-${rectangle.id}` || name === `ground-label-bg-${rectangle.id}`;
            }).forEach(node => node.destroy());

            // 添加新标签
            this.addGroundRectangleLabel(rectangle);
        },

        // 编辑地面矩形信息
        editGroundRectangleInfo(rectangleId) {
            const rectangle = this.groundRectangleList.find(r => r.id === rectangleId);
            if (rectangle) {
                this.editingGroundRectangle = {
                    id: rectangle.id,
                    name: rectangle.name,
                    description: rectangle.description || ''
                };
                this.showEditGroundRectangleModal = true;
            }
        },

        // 确认编辑地面矩形
        confirmEditGroundRectangle() {
            if (!this.editingGroundRectangle.name.trim()) {
                this.showMessage('请输入矩形名称', 'error');
                return;
            }

            const rectangle = this.groundRectangleList.find(r => r.id === this.editingGroundRectangle.id);
            if (rectangle) {
                rectangle.name = this.editingGroundRectangle.name.trim();
                rectangle.description = this.editingGroundRectangle.description.trim();
                this.updateGroundRectangleLabel(rectangle);
                this.showMessage(`地面矩形信息更新成功: ${rectangle.name}`, 'success');
            }

            this.cancelEditGroundRectangle();
        },

        // 取消编辑地面矩形
        cancelEditGroundRectangle() {
            this.showEditGroundRectangleModal = false;
            this.editingGroundRectangle = {
                id: '',
                name: '',
                description: ''
            };
        },

        // 确认新区域
        confirmNewRegion() {
            if (!this.newRegionName.trim()) {
                this.showMessage('请输入区域名称', 'error');
                return;
            }

            if (!this.newRegionDeviceID.trim()) {
                this.showMessage('请输入设备ID', 'error');
                return;
            }

            const newRegion = {
                id: `region_${Date.now()}`,
                name: this.newRegionName.trim(),
                device_id: this.newRegionDeviceID.trim(),
                points: [...this.currentPoints]
            };

            this.regionList.push(newRegion);
            this.drawRegion(newRegion);

            // 重置状态
            this.showRegionForm = false;
            this.newRegionName = '';
            this.newRegionDeviceID = '';
            this.currentPoints = [];

            this.showMessage(`区域 "${newRegion.name}" (设备ID: ${newRegion.device_id}) 添加成功`, 'success');
        },

        // 取消绘制
        cancelDrawing() {
            this.isDrawing = false;
            this.currentPoints = [];
            this.clearTempElements();
            this.showMessage('绘制已取消', 'success');
        },

        // 取消新区域
        cancelNewRegion() {
            this.showRegionForm = false;
            this.newRegionName = '';
            this.newRegionDeviceID = '';
            this.currentPoints = [];
            this.clearTempElements();
        },

        // 选择区域
        selectRegion(regionId) {
            this.selectedRegionId = regionId;

            // 高亮选中的区域
            this.highlightRegion(regionId);
        },

        // 高亮区域
        highlightRegion(regionId) {
            // 重置所有区域样式
            this.stage.find('Line').forEach(line => {
                if (line.name().startsWith('region-')) {
                    line.stroke('#ffc107');     // 默认黄色
                    line.strokeWidth(3);
                }
            });

            // 高亮选中区域
            const selectedRegion = this.stage.findOne(`.region-${regionId}`);
            if (selectedRegion) {
                selectedRegion.stroke('#ff4757');  // 选中时红色
                selectedRegion.strokeWidth(4);     // 更粗的边框
            }

            this.layer.draw();
        },

        // 删除区域
        deleteRegion(regionId) {
            const region = this.regionList.find(r => r.id === regionId);
            if (region) {
                this.deletingRegion = {
                    id: region.id,
                    name: region.name,
                    device_id: region.device_id || ''
                };
                this.showDeleteRegionModal = true;
            }
        },

        // 确认删除区域
        confirmDeleteRegion() {
            // 从列表中移除
            this.regionList = this.regionList.filter(r => r.id !== this.deletingRegion.id);

            // 从画布中移除
            this.removeRegionFromCanvas(this.deletingRegion.id);

            // 重置选择
            if (this.selectedRegionId === this.deletingRegion.id) {
                this.selectedRegionId = null;
            }

            this.showMessage(`区域 "${this.deletingRegion.name}" 删除成功`, 'success');
            this.cancelDeleteRegion();
        },

        // 取消删除区域
        cancelDeleteRegion() {
            this.showDeleteRegionModal = false;
            this.deletingRegion = {
                id: '',
                name: '',
                device_id: ''
            };
        },

        // 从画布移除区域
        removeRegionFromCanvas(regionId) {
            this.stage.find(node => {
                const name = node.name();
                return name.includes(regionId) && (
                    name.startsWith('region-') ||
                    name.startsWith('vertex-') ||
                    name.startsWith('label-')
                );
            }).forEach(node => node.destroy());

            this.layer.draw();
        },

        // 清空画布
        clearCanvas() {
            this.showClearCanvasModal = true;
        },

        // 确认清空画布
        confirmClearCanvas() {
            this.regionList = [];
            this.selectedRegionId = null;

            // 只清除区域相关的元素，保留背景图片
            this.clearRegionsFromCanvas();

            this.showMessage('所有区域已清空', 'success');
            this.cancelClearCanvas();
        },

        // 取消清空画布
        cancelClearCanvas() {
            this.showClearCanvasModal = false;
        },

        // 清除画布上的所有区域（保留背景）
        clearRegionsFromCanvas() {
            // 清除所有区域相关的元素
            this.stage.find(node => {
                const name = node.name();
                return name.startsWith('region-') ||
                       name.startsWith('vertex-') ||
                       name.startsWith('label-') ||
                       name.startsWith('temp-') ||
                       name.startsWith('finish-hint');
            }).forEach(node => node.destroy());

            this.layer.draw();
        },

        // 更新区域顶点
        updateRegionFromVertex(regionId, vertexIndex, newX, newY) {
            const region = this.regionList.find(r => r.id === regionId);
            if (region && region.points[vertexIndex]) {
                region.points[vertexIndex] = [newX, newY];

                // 更新多边形
                const polygon = this.stage.findOne(`.region-${regionId}`);
                if (polygon) {
                    polygon.points(region.points.flat());
                }

                // 更新标签位置
                this.updateRegionLabel(region);

                this.layer.draw();
            }
        },

        // 更新区域标签
        updateRegionLabel(region) {
            // 移除旧标签
            this.stage.find(node => {
                const name = node.name();
                return name === `label-${region.id}` || name === `label-bg-${region.id}`;
            }).forEach(node => node.destroy());

            // 添加新标签
            this.addRegionLabel(region);
        },

        // 保存区域
        async saveRegions() {
            try {
                // 转换坐标为整数
                const regionsWithIntCoords = this.regionList.map(region => ({
                    ...region,
                    points: region.points.map(point => [
                        Math.round(point[0]), // 转换为整数
                        Math.round(point[1])  // 转换为整数
                    ])
                }));

                const response = await fetch(`/api/cameras/${this.editingRegionCamera.id}/regions`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        regions: regionsWithIntCoords
                    })
                });

                const result = await response.json();

                if (result.success) {
                    this.showMessage('区域保存成功', 'success');

                    // 更新本地摄像头数据
                    const camera = this.cameras.find(c => c.id === this.editingRegionCamera.id);
                    if (camera) {
                        camera.regions = [...this.regionList];
                    }

                    this.closeRegionEditor();
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                this.showMessage('保存区域失败: ' + error.message, 'error');
            }
        },

        // 关闭区域编辑器
        closeRegionEditor() {
            this.showRegionEditor = false;
            this.editingRegionCamera = null;
            this.regionList = [];
            this.selectedRegionId = null;
            this.isDrawing = false;
            this.showRegionForm = false;
            this.newRegionName = '';
            this.newRegionDeviceID = '';
            this.currentPoints = [];
            this.showInstructions = true;  // 重置为显示操作说明

            // 清理ResizeObserver
            if (this.resizeObserver) {
                this.resizeObserver.disconnect();
                this.resizeObserver = null;
            }

            // 清理Konva舞台
            if (this.stage) {
                this.stage.destroy();
                this.stage = null;
                this.layer = null;
            }
        },

        // 刷新视频帧
        async refreshVideoFrame() {
            this.showMessage('正在刷新视频帧...', 'success');

            try {
                // 保存当前区域
                const currentRegions = [...this.regionList];
                const currentSelected = this.selectedRegionId;

                // 清除画布上的所有区域
                this.clearRegionsFromCanvas();

                // 重新加载视频帧
                await this.loadVideoFrame();

                // 恢复区域
                this.regionList = currentRegions;
                this.selectedRegionId = currentSelected;
                this.renderExistingRegions();

                this.showMessage('视频帧刷新成功', 'success');
            } catch (error) {
                console.error('刷新视频帧失败:', error);
                this.showMessage('刷新视频帧失败: ' + error.message, 'error');
            }
        },

        // 编辑区域信息
        editRegionInfo(regionId) {
            const region = this.regionList.find(r => r.id === regionId);
            if (region) {
                this.editingRegion = {
                    id: region.id,
                    name: region.name,
                    device_id: region.device_id || ''
                };
                this.showEditRegionModal = true;
            }
        },

        // 确认编辑区域
        confirmEditRegion() {
            if (!this.editingRegion.name.trim()) {
                this.showMessage('请输入区域名称', 'error');
                return;
            }

            if (!this.editingRegion.device_id.trim()) {
                this.showMessage('请输入设备ID', 'error');
                return;
            }

            const region = this.regionList.find(r => r.id === this.editingRegion.id);
            if (region) {
                region.name = this.editingRegion.name.trim();
                region.device_id = this.editingRegion.device_id.trim();
                this.updateRegionLabel(region);
                this.showMessage(`区域信息更新成功: ${region.name} (设备ID: ${region.device_id})`, 'success');
            }

            this.cancelEditRegion();
        },

        // 删除地面矩形
        deleteGroundRectangle(rectangleId) {
            const rectangle = this.groundRectangleList.find(r => r.id === rectangleId);
            if (rectangle) {
                this.deletingGroundRectangle = {
                    id: rectangle.id,
                    name: rectangle.name
                };
                this.showDeleteGroundRectangleModal = true;
            }
        },

        // 确认删除地面矩形
        confirmDeleteGroundRectangle() {
            // 从列表中移除
            this.groundRectangleList = this.groundRectangleList.filter(r => r.id !== this.deletingGroundRectangle.id);

            // 从画布中移除
            this.removeGroundRectangleFromCanvas(this.deletingGroundRectangle.id);

            // 重置选择
            if (this.selectedGroundRectangleId === this.deletingGroundRectangle.id) {
                this.selectedGroundRectangleId = null;
            }

            this.showMessage(`地面矩形 "${this.deletingGroundRectangle.name}" 删除成功`, 'success');
            this.cancelDeleteGroundRectangle();
        },

        // 取消删除地面矩形
        cancelDeleteGroundRectangle() {
            this.showDeleteGroundRectangleModal = false;
            this.deletingGroundRectangle = {
                id: '',
                name: ''
            };
        },

        // 从画布移除地面矩形
        removeGroundRectangleFromCanvas(rectangleId) {
            this.groundStage.find(node => {
                const name = node.name();
                return name.includes(rectangleId) && (
                    name.startsWith('ground-rect-') ||
                    name.startsWith('ground-vertex-') ||
                    name.startsWith('ground-label-')
                );
            }).forEach(node => node.destroy());
            this.groundLayer.draw();
        },

        // 清空地面画布
        clearGroundCanvas() {
            this.showClearGroundCanvasModal = true;
        },

        // 确认清空地面画布
        confirmClearGroundCanvas() {
            this.groundRectangleList = [];
            this.selectedGroundRectangleId = null;

            // 只清除矩形相关的元素，保留背景图片
            this.clearGroundRectanglesFromCanvas();

            this.showMessage('所有地面矩形已清空', 'success');
            this.cancelClearGroundCanvas();
        },

        // 取消清空地面画布
        cancelClearGroundCanvas() {
            this.showClearGroundCanvasModal = false;
        },

        // 清除画布上的所有地面矩形（保留背景）
        clearGroundRectanglesFromCanvas() {
            // 清除所有矩形相关的元素
            this.groundStage.find(node => {
                const name = node.name();
                return name.startsWith('ground-rect-') ||
                       name.startsWith('ground-vertex-') ||
                       name.startsWith('ground-label-') ||
                       name.startsWith('temp-ground-');
            }).forEach(node => node.destroy());
            this.groundLayer.draw();
        },

        // 刷新地面视频帧
        async refreshGroundVideoFrame() {
            if (!this.editingGroundCalibrationCamera) {
                this.showMessage('没有有效的摄像头', 'error');
                return;
            }

            this.showMessage('正在刷新视频帧...', 'success');

            try {
                // 保存当前矩形
                const currentRectangles = [...this.groundRectangleList];
                const currentSelected = this.selectedGroundRectangleId;

                // 清除画布上的所有矩形
                this.clearGroundRectanglesFromCanvas();

                // 重新加载视频帧
                await this.loadGroundVideoFrame();

                // 恢复矩形
                this.groundRectangleList = currentRectangles;
                this.selectedGroundRectangleId = currentSelected;
                this.renderExistingGroundRectangles();

                this.showMessage('视频帧刷新成功', 'success');
            } catch (error) {
                this.showMessage('刷新视频帧失败: ' + error.message, 'error');
            }
        },

        // 保存平面标定
        async saveGroundCalibration() {
            try {
                // 转换坐标为整数
                const rectanglesWithIntCoords = this.groundRectangleList.map(rectangle => ({
                    ...rectangle,
                    points: rectangle.points.map(point => [
                        Math.round(point[0]), // 转换为整数
                        Math.round(point[1])  // 转换为整数
                    ])
                }));

                const response = await fetch(`/api/cameras/${this.editingGroundCalibrationCamera.id}/ground-calibration`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        enabled: this.groundRectangleList.length > 0,
                        rectangles: rectanglesWithIntCoords
                    })
                });

                const result = await response.json();
                if (result.success) {
                    this.showMessage('平面标定保存成功', 'success');

                    // 更新本地摄像头数据
                    const camera = this.cameras.find(c => c.id === this.editingGroundCalibrationCamera.id);
                    if (camera) {
                        camera.ground_calibration = {
                            enabled: this.groundRectangleList.length > 0,
                            rectangles: [...this.groundRectangleList]
                        };
                    }

                    this.closeGroundCalibrationEditor();
                } else {
                    throw new Error(result.message);
                }
            } catch (error) {
                this.showMessage('保存平面标定失败: ' + error.message, 'error');
            }
        },

        // 关闭平面标定编辑器
        closeGroundCalibrationEditor() {
            this.showGroundCalibrationEditor = false;
            this.editingGroundCalibrationCamera = null;
            this.groundRectangleList = [];
            this.selectedGroundRectangleId = null;
            this.isDrawingGround = false;
            this.showGroundRectangleForm = false;
            this.newGroundRectangleName = '';
            this.newGroundRectangleDescription = '';
            this.currentGroundPoints = [];
            this.showGroundInstructions = true;

            // 清理ResizeObserver
            if (this.groundResizeObserver) {
                this.groundResizeObserver.disconnect();
                this.groundResizeObserver = null;
            }

            // 清理Konva舞台
            if (this.groundStage) {
                this.groundStage.destroy();
                this.groundStage = null;
                this.groundLayer = null;
            }

            // 重置视频尺寸
            this.actualGroundVideoWidth = null;
            this.actualGroundVideoHeight = null;
        },

        // 取消编辑区域
        cancelEditRegion() {
            this.showEditRegionModal = false;
            this.editingRegion = {
                id: '',
                name: '',
                device_id: ''
            };
        },

        // 日志查看器相关方法
        openLogViewer(instanceId, instanceName) {
            this.logInstanceId = instanceId;
            this.logInstanceName = instanceName;
            this.logDate = new Date().toISOString().split('T')[0]; // 今天的日期
            this.logLevel = '';
            this.logDisplay = '';
            this.showLogViewer = true;
            this.loadLogs();
        },

        closeLogViewer() {
            this.showLogViewer = false;
            this.logInstanceId = '';
            this.logInstanceName = '';
            this.logDate = '';
            this.logLevel = '';
            this.logDisplay = '';
        },

        async loadLogs() {
            if (!this.logInstanceId || !this.logDate) {
                return;
            }

            this.logLoading = true;
            try {
                const response = await fetch(`/api/instances/${this.logInstanceId}/logs?date=${this.logDate}&level=${this.logLevel}`);
                if (response.ok) {
                    const data = await response.json();
                    const rawLogs = data.logs || '暂无日志数据';

                    // 处理文本格式日志并计算统计信息
                    this.originalLogDisplay = rawLogs;
                    this.logDisplay = rawLogs;
                    this.calculateLogStats(rawLogs);

                    // 等待DOM更新后应用语法高亮
                    this.$nextTick(() => {
                        this.applyHighlight();
                    });
                } else {
                    this.logDisplay = '加载日志失败: ' + response.statusText;
                }
            } catch (error) {
                this.logDisplay = '加载日志失败: ' + error.message;
            } finally {
                this.logLoading = false;
            }
        },

        applyHighlight() {
            const codeElement = this.$refs.logCode;
            if (codeElement) {
                // 应用文本格式的语法高亮
                const highlightedContent = this.highlightTextLogs(this.logDisplay);
                codeElement.innerHTML = highlightedContent;
            }
        },

        highlightTextLogs(logText) {
            if (!logText || logText.includes('暂无日志') || logText.includes('加载日志失败')) {
                return `<div class="log-empty">${logText}</div>`;
            }

            // 按行处理日志
            const lines = logText.split('\n').filter(line => line.trim());
            const highlightedLines = lines.map((line) => {
                // 使用正则表达式匹配日志格式：[时间戳] [级别] [模块] 消息内容
                const logPattern = /^(\[[\d-: .]+\])\s*(\[[\w ]+\])\s*(\[[\w ]+\])\s*(.*)$/;
                const match = line.match(logPattern);

                if (match) {
                    const [, timestamp, level, module, message] = match;
                    const levelText = level.replace(/[\[\]]/g, '').trim();

                    return `<div class="log-line">` +
                           `<span class="log-timestamp">${timestamp}</span> ` +
                           `<span class="log-level log-level-${levelText.toLowerCase()}">${level}</span> ` +
                           `<span class="log-module">${module}</span>` +
                           `<span class="log-message">${message}</span>` +
                           `</div>`;
                } else {
                    // 如果不匹配格式，直接返回原始行
                    return `<div class="log-line"><span class="log-raw">${line}</span></div>`;
                }
            });

            return highlightedLines.join('');
        },

        calculateLogStats(rawLogs) {
            // 重置统计
            this.logStats = {
                total: 0,
                info: 0,
                warn: 0,
                error: 0,
                debug: 0
            };

            if (!rawLogs || rawLogs.includes('暂无日志') || rawLogs.includes('加载日志失败')) {
                return;
            }

            try {
                const lines = rawLogs.split('\n').filter(line => line.trim());
                this.logStats.total = lines.length;

                for (const line of lines) {
                    // 使用正则表达式提取日志级别
                    const levelMatch = line.match(/\[[\d-: .]+\]\s*\[([\w ]+)\]/);
                    if (levelMatch) {
                        const level = levelMatch[1].trim().toUpperCase();

                        switch (level) {
                            case 'INFO':
                                this.logStats.info++;
                                break;
                            case 'WARN':
                                this.logStats.warn++;
                                break;
                            case 'ERROR':
                                this.logStats.error++;
                                break;
                            case 'DEBUG':
                                this.logStats.debug++;
                                break;
                        }
                    }
                }
            } catch (error) {
                console.error('计算日志统计失败:', error);
            }
        },

        filterLogs() {
            this.loadLogs();
        },

        refreshLogs() {
            this.loadLogs();
        },

        // 系统配置相关方法
        async openSystemConfig() {
            this.showSystemConfig = true;
            this.activeConfigTab = 'detection';
            await this.loadSystemConfig();
        },

        closeSystemConfig() {
            this.showSystemConfig = false;
        },

        async loadSystemConfig() {
            try {
                const response = await fetch('/api/config');
                if (response.ok) {
                    const config = await response.json();
                    console.log('Loaded config:', config); // 调试信息

                    // 加载检测配置
                    if (config.detection) {
                        this.systemConfig.detection.confidence_threshold = config.detection.confidence_threshold || 0.3;
                        this.systemConfig.detection.continuous_count = config.detection.continuous_count || 10;
                    }

                    // 加载报警配置
                    if (config.alarm) {
                        this.systemConfig.alarm.alarm_url = config.alarm.alarm_url || '';
                        this.systemConfig.alarm.alarm_header.tenant_key = config.alarm.alarm_header?.tenant_key || '';
                        this.systemConfig.alarm.cooldown_minute = config.alarm.cooldown_minute || 20;
                        this.systemConfig.alarm.progressive_cooldown.enabled = config.alarm.progressive_cooldown?.enabled !== undefined ? config.alarm.progressive_cooldown.enabled : true;
                        this.systemConfig.alarm.progressive_cooldown.reset_cooldown_minutes = config.alarm.progressive_cooldown?.reset_cooldown_minutes || 5;
                    }

                    // 加载日志配置
                    if (config.log) {
                        this.systemConfig.log.retention_days = config.log.retention_days || 7;
                    }

                    console.log('Updated systemConfig:', this.systemConfig); // 调试信息
                } else {
                    this.showMessage('加载系统配置失败', 'error');
                }
            } catch (error) {
                this.showMessage('加载系统配置失败: ' + error.message, 'error');
                console.error('Config load error:', error);
            }
        },

        async saveSystemConfig() {
            try {
                const response = await fetch('/api/config', {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        detection: this.systemConfig.detection,
                        alarm: this.systemConfig.alarm,
                        log: this.systemConfig.log
                    })
                });

                if (response.ok) {
                    this.showMessage('系统配置保存成功', 'success');
                    this.closeSystemConfig();
                } else {
                    this.showMessage('保存系统配置失败', 'error');
                }
            } catch (error) {
                this.showMessage('保存系统配置失败: ' + error.message, 'error');
            }
        },

        searchLogs() {
            if (!this.logSearchTerm.trim()) {
                // 如果搜索词为空，显示原始日志
                this.logDisplay = this.originalLogDisplay;
            } else {
                // 过滤包含搜索词的日志行
                const lines = this.originalLogDisplay.split('\n');
                const filteredLines = lines.filter(line =>
                    line.toLowerCase().includes(this.logSearchTerm.toLowerCase())
                );
                this.logDisplay = filteredLines.join('\n');
            }

            // 重新应用语法高亮
            this.$nextTick(() => {
                this.applyHighlight();
            });
        },
    };
}
