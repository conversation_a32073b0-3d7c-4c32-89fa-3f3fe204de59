/*
 * @Author: Li RF
 * @Date: 2024-11-26 10:07:30
 * @LastEditors: Li RF
 * @LastEditTime: 2025-03-20 17:39:38
 * @Description: 
 * Email: <EMAIL>
 * Copyright (c) 2024 Li RF, All Rights Reserved.
 */
#ifndef RKYOLOV5S_H
#define RKYOLOV5S_H

#include "rknn_api.h"
#include "opencv2/core/core.hpp"
#include "SharedTypes.hpp"

// 前向声明
class RegionDetectionManager;

// YOLO模型类型枚举
enum class YoloModelType {
    YOLOV5,
    YOLOV8
};

static void dump_tensor_attr(rknn_tensor_attr *attr);
static unsigned char *load_data(FILE *fp, size_t ofst, size_t sz);
static unsigned char *load_model(const char *filename, int *model_size);

class rkYolo
{
private:
    int ret;
    std::mutex mtx;
    AppConfig config;

    rknn_context ctx;
    rknn_input_output_num io_num;
    // rknn_tensor_attr *input_attrs;
    // rknn_tensor_attr *output_attrs;
    // 更新为智能指针
    std::unique_ptr<rknn_tensor_attr[]> input_attrs;
    std::unique_ptr<rknn_tensor_attr[]> output_attrs;
    rknn_input inputs[1];

    int channel, width, height;

    float nms_threshold, box_conf_threshold;

    YoloModelType model_type;  // 模型类型
    bool labels_initialized;   // 标签是否已初始化

    // 区域检测管理器
    std::unique_ptr<RegionDetectionManager> region_manager;

public:
    rkYolo(const AppConfig& config);
    int init(rknn_context *ctx_in, bool isChild);
    rknn_context *get_pctx();
    cv::Mat infer(cv::Mat ori_img);
    cv::Mat infer_from_nv12(const uint8_t* nv12_data, int width, int height, cv::Mat& bgr_for_display);
    ~rkYolo();
};

#endif