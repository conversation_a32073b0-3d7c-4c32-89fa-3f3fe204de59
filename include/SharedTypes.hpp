/*
 * @Author: Li RF
 * @Date: 2025-03-14 09:16:36
 * @LastEditors: Li RF
 * @LastEditTime: 2025-03-22 16:54:33
 * @Description: 
 * Email: <EMAIL>
 * Copyright (c) 2025 Li RF, All Rights Reserved.
 */
#ifndef SHAREDTYPES_H
#define SHAREDTYPES_H

#include <string>
#include <vector>
using namespace std;

/* NPU 数量 */
const int NPU_CORE_NUM = 3;

enum ACCELS_2D {
    ACC_OPENCV = 1,
    ACC_RGA = 2,
};

enum INPUT_FORMAT {
    IN_VIDEO = 1,
    IN_CAMERA = 2,
};

enum READ_ENGINE {
    EN_FFMPEG = 1,
    EN_OPENCV = 2,
};

enum YOLO_MODEL_TYPE {
    YOLO_V5 = 5,
    YOLO_V8 = 8,
};

/* 检测区域结构体 */
struct DetectionRegion {
    string id;                              // 区域ID
    string name;                            // 区域名称
    string device_id;                       // 设备ID
    vector<pair<int, int>> points;          // 多边形顶点坐标
};

/* 平面标定矩形结构体 */
struct GroundRectangle {
    string id;                              // 矩形ID
    string name;                            // 矩形名称
    vector<pair<int, int>> points;          // 矩形四个角点坐标
    string description;                     // 描述信息
};

/* 平面标定配置结构体 */
struct GroundCalibration {
    bool enabled;                           // 是否启用平面标定
    vector<GroundRectangle> rectangles;     // 平面标定矩形列表
};

/* 目标位置信息结构体 */
struct TargetPositionInfo {
    string object_type;                     // 目标类型
    float confidence;                       // 检测置信度
    float width_ratio;                      // 宽度占比 (0-1)
    bool calibration_valid;                 // 标定是否有效
};

/* 渐进式冷却配置结构体 */
struct ProgressiveCooldownConfig {
    bool enabled;                          // 是否启用渐进式冷却
    float reset_cooldown_minutes;          // 无检测多少分钟后完全重置冷却期
};

/* 定义命令行参数结构体 */ 
struct AppConfig {
    // 在屏幕显示 FPS
    bool screen_fps = false;
    // 在终端打印 FPS
    bool print_fps = false;
    // 是否使用opencl
    bool opencl = true;
    // 是否打印命令行参数
    bool verbose = false;
    // 视频加载引擎，默认为 ffmpeg
    int read_engine = READ_ENGINE::EN_FFMPEG;
    // 输入格式，默认为视频
    int input_format = INPUT_FORMAT::IN_VIDEO;
    // 硬件加速，默认为 RGA
    int accels_2d = ACCELS_2D::ACC_RGA;
    // 线程数，默认为1
    int threads = 1;
    // rknn 模型路径
    string model_path = "";
    // 输入源    
    string input = "";
    // 解码器，默认为 h264_rkmpp
    string decodec = "h264_rkmpp";
    // YOLO模型类型，默认为YOLOv5
    int yolo_model_type = YOLO_MODEL_TYPE::YOLO_V5;
    // 标签文件路径，默认为空（使用内置标签）
    string labels_path = "";

    // 报警相关配置
    vector<DetectionRegion> regions;        // 检测区域列表
    float confidence_threshold = 0.4f;      // 置信度阈值
    int continuous_count = 10;              // 连续检测次数阈值
    int cooldown_minutes = 20;              // 冷却时间（分钟）
    string alarm_url = "";                  // 报警接口URL
    string alarm_header = "{}";             // 报警请求头
    string device_id = "";                  // 设备ID
    bool draw_regions = false;              // 是否绘制区域框
    ProgressiveCooldownConfig progressive_cooldown; // 渐进式冷却配置

    // 平面标定配置
    GroundCalibration ground_calibration;   // 平面标定配置

    // 日志配置
    bool log_enable_file = true;            // 是否启用文件日志
    bool log_enable_console = true;         // 是否启用控制台日志
    string log_min_level = "INFO";          // 最小日志级别
    string instance_name = "";              // 实例名称（用于区分不同实例，为空时使用进程ID）
    int log_retention_days = 7;             // 日志保留天数（超过自动删除）

    // 重连配置
    bool enable_reconnect = true;           // 是否启用自动重连
    int max_reconnect_attempts = 5;         // 最大重连次数
    int reconnect_delay_seconds = 5;        // 重连间隔（秒）
};


#endif // SHAREDTYPES_H