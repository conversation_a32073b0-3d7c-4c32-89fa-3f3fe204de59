/*
 * @Author: <PERSON> RF
 * @Date: 2025-01-23 10:00:00
 * @LastEditors: Li RF
 * @LastEditTime: 2025-01-23 10:00:00
 * @Description: 区域检测算法实现
 * Email: <EMAIL>
 * Copyright (c) 2025 Li RF, All Rights Reserved.
 */
#ifndef REGION_DETECTION_HPP
#define REGION_DETECTION_HPP

#include <vector>
#include <string>
#include <map>
#include <chrono>
#include "SharedTypes.hpp"
#include "postprocess.h"
#include "opencv2/opencv.hpp"

// FreeType支持中文字体
#ifdef WITH_FREETYPE
#include "opencv2/freetype.hpp"
#endif

using namespace std;

/**
 * @Description: 区域检测状态结构体
 */
struct RegionDetectionState {
    int continuous_count;           // 当前连续检测次数
    chrono::steady_clock::time_point last_alarm_time;  // 上次报警时间
    chrono::steady_clock::time_point last_detection_time; // 上次检测到目标的时间
    bool is_in_cooldown;           // 是否在冷却期
    float last_confidence;         // 上次检测的最高置信度

    RegionDetectionState() : continuous_count(0), is_in_cooldown(false), last_confidence(0.0f) {}
};

/**
 * @Description: 区域检测管理器类
 */
class RegionDetectionManager {
private:
    AppConfig config;                                    // 配置信息
    map<string, RegionDetectionState> region_states;    // 每个区域的检测状态

#ifdef WITH_FREETYPE
    cv::Ptr<cv::freetype::FreeType2> ft2;               // FreeType字体渲染器
    bool font_loaded;                                    // 字体是否加载成功
#endif
    
public:
    /**
     * @Description: 构造函数
     * @param {AppConfig&} app_config: 应用配置
     */
    RegionDetectionManager(const AppConfig& app_config);
    

    

    

    
    /**
     * @Description: 处理检测结果，更新区域状态并判断是否需要报警
     * @param {detect_result_group_t&} detections: 检测结果组
     * @param {cv::Mat&} image: 当前帧图像（用于报警时发送）
     * @return {vector<string>}: 需要报警的区域ID列表
     */
    vector<string> processDetections(const detect_result_group_t& detections, cv::Mat& image);
    
    /**
     * @Description: 检查区域是否在冷却期
     * @param {string&} region_id: 区域ID
     * @return {bool}: true表示在冷却期，false表示不在
     */
    bool isRegionInCooldown(const string& region_id);
    
    /**
     * @Description: 重置区域的连续检测计数
     * @param {string&} region_id: 区域ID
     */
    void resetRegionCount(const string& region_id);
    
    /**
     * @Description: 设置区域进入冷却期
     * @param {string&} region_id: 区域ID
     */
    void setRegionCooldown(const string& region_id);
    
    /**
     * @Description: 获取区域当前状态信息（用于调试）
     * @param {string&} region_id: 区域ID
     * @return {RegionDetectionState}: 区域状态
     */
    RegionDetectionState getRegionState(const string& region_id);
    
    /**
     * @Description: 打印所有区域状态（用于调试）
     */
    void printRegionStates();

    /**
     * @Description: 在图像上绘制所有检测区域
     * @param {cv::Mat&} image: 要绘制的图像
     */
    void drawRegions(cv::Mat& image);

    /**
     * @Description: 在图像上绘制单个区域
     * @param {cv::Mat&} image: 要绘制的图像
     * @param {DetectionRegion&} region: 要绘制的区域
     * @param {cv::Scalar} color: 绘制颜色
     * @param {int} thickness: 线条粗细
     */
    void drawSingleRegion(cv::Mat& image, const DetectionRegion& region,
                         const cv::Scalar& color = cv::Scalar(0, 255, 0), int thickness = 2);

private:
    /**
     * @Description: 初始化中文字体
     * @param {string} fontPath: 字体文件路径
     * @return {bool}: 是否初始化成功
     */
    bool initChineseFont(const string& fontPath = "");

    /**
     * @Description: 绘制中文文字
     * @param {cv::Mat&} image: 要绘制的图像
     * @param {string} text: 要绘制的文字
     * @param {cv::Point} position: 文字位置
     * @param {int} fontSize: 字体大小
     * @param {cv::Scalar} color: 文字颜色
     * @param {int} thickness: 文字粗细
     */
    void putChineseText(cv::Mat& image, const string& text, cv::Point position,
                       int fontSize = 16, const cv::Scalar& color = cv::Scalar(255, 255, 255),
                       int thickness = 1);

    /**
     * @Description: 获取系统中文字体路径
     * @return {string}: 字体文件路径
     */
    string getSystemChineseFontPath();

    /**
     * @Description: 发送HTTP报警请求
     * @param {string} region_id: 区域ID
     * @param {string} region_name: 区域名称
     * @param {float} confidence: 检测置信度
     * @param {cv::Mat} alarm_image: 报警图片（已绘制检测框）
     * @return {bool}: 是否发送成功
     */
    bool sendAlarmRequest(const string& region_id, const string& region_name,
                         float confidence, const cv::Mat& alarm_image);

    /**
     * @Description: 将图片编码为base64字符串
     * @param {cv::Mat} image: 输入图片
     * @return {string}: base64编码字符串
     */
    string encodeImageToBase64(const cv::Mat& image);

    /**
     * @Description: 获取当前时间字符串
     * @return {string}: 格式化的时间字符串 "YYYY-MM-DD HH:MM:SS"
     */
    string getCurrentTimeString();

    /**
     * @Description: 记录报警日志
     * @param {string} region_name: 区域名称
     * @param {float} confidence: 检测置信度
     */
    void logAlarm(const string& region_name, float confidence);
};

#endif // REGION_DETECTION_HPP
