#ifndef GLOBAL_ALARM_MANAGER_HPP
#define GLOBAL_ALARM_MANAGER_HPP

#include <string>
#include <map>
#include <mutex>
#include <chrono>
#include <vector>
#include "SharedTypes.hpp"
#include "postprocess.h"
#include "GroundCalibrationManager.hpp"
#include "opencv2/opencv.hpp"

using namespace std;

/**
 * @Description: 全局报警状态结构体
 */
struct GlobalAlarmState {
    int continuous_count;           // 当前连续检测次数
    chrono::steady_clock::time_point last_alarm_time;  // 上次报警时间
    chrono::steady_clock::time_point last_detection_time; // 上次检测到目标的时间
    chrono::steady_clock::time_point no_detection_start_time; // 开始无检测的时间
    bool is_in_cooldown;           // 是否在冷却期
    bool is_tracking_no_detection; // 是否正在跟踪无检测状态
    bool has_detection_in_current_frame; // 当前帧是否有检测
    float last_confidence;         // 上次检测的最高置信度

    GlobalAlarmState() : continuous_count(0), is_in_cooldown(false),
                        is_tracking_no_detection(false), has_detection_in_current_frame(false),
                        last_confidence(0.0f) {
        auto now = chrono::steady_clock::now();
        last_detection_time = now;
        no_detection_start_time = now;
    }
};

/**
 * @Description: 全局报警管理器（单例模式）
 * 负责管理所有线程的报警状态，确保每个区域只发送一次报警
 */
class GlobalAlarmManager {
private:
    static GlobalAlarmManager* instance;
    static mutex instance_mutex;
    
    AppConfig config;                                    // 配置信息
    map<string, GlobalAlarmState> region_states;        // 每个区域的报警状态
    mutex states_mutex;                                  // 状态访问互斥锁
    unique_ptr<GroundCalibrationManager> calibration_manager; // 平面标定管理器
    
    // 私有构造函数（单例模式）
    GlobalAlarmManager(const AppConfig& app_config);
    
public:
    /**
     * @Description: 获取全局报警管理器实例
     * @param {AppConfig&} config: 应用配置（仅在首次调用时使用）
     * @return {GlobalAlarmManager*}: 全局实例指针
     */
    static GlobalAlarmManager* getInstance(const AppConfig& config);
    
    /**
     * @Description: 获取全局报警管理器实例（已初始化后使用）
     * @return {GlobalAlarmManager*}: 全局实例指针
     */
    static GlobalAlarmManager* getInstance();
    
    /**
     * @Description: 销毁全局报警管理器实例
     */
    static void destroyInstance();

    /**
     * @Description: 加载平面标定配置
     * @param {GroundCalibration&} calibration: 平面标定配置
     * @param {int} image_width: 图像宽度
     * @param {int} image_height: 图像高度
     */
    void loadGroundCalibration(const GroundCalibration& calibration, int image_width, int image_height);
    
    /**
     * @Description: 处理检测结果并判断是否需要报警（线程安全）
     * @param {detect_result_group_t&} detections: 检测结果
     * @param {cv::Mat&} image: 当前帧图像（用于报警时发送）
     * @return {vector<string>}: 触发报警的区域ID列表
     */
    vector<string> processDetections(const detect_result_group_t& detections, cv::Mat& image);
    
    /**
     * @Description: 检查区域是否在冷却期（线程安全）
     * @param {string&} region_id: 区域ID
     * @return {bool}: true表示在冷却期，false表示不在
     */
    bool isRegionInCooldown(const string& region_id);
    
    /**
     * @Description: 设置区域冷却期（线程安全）
     * @param {string&} region_id: 区域ID
     */
    void setRegionCooldown(const string& region_id);
    
    /**
     * @Description: 重置区域连续计数（线程安全）
     * @param {string&} region_id: 区域ID
     */
    void resetRegionCount(const string& region_id);
    
    /**
     * @Description: 打印所有区域状态（调试用）
     */
    void printRegionStates();
    
private:
    /**
     * @Description: 点在多边形内判断算法（射线投射法）
     */
    bool isPointInPolygon(int x, int y, const vector<pair<int, int>>& polygon);
    
    /**
     * @Description: 获取检测框底线的中心点
     */
    pair<int, int> getBoxBottomCenter(const BOX_RECT& box);

    /**
     * @Description: 检查检测结果是否在指定区域内
     */
    bool isDetectionInRegion(const detect_result_t& detection, const DetectionRegion& region);

    /**
     * @Description: 计算目标的位置信息（基于平面标定）
     * @param {detect_result_t&} detection: 检测结果
     * @param {GroundCalibration&} calibration: 平面标定配置
     * @return {TargetPositionInfo}: 目标位置信息
     */
    TargetPositionInfo calculateTargetPosition(const detect_result_t& detection, const GroundCalibration& calibration);
    
    /**
     * @Description: 发送HTTP报警请求
     */
    bool sendAlarmRequest(const string& region_id, const string& region_name,
                         const string& device_id, float confidence, const cv::Mat& alarm_image,
                         const detect_result_t* detection = nullptr, const GroundCalibration* calibration = nullptr);
    
    /**
     * @Description: 将图片编码为base64字符串
     */
    string encodeImageToBase64(const cv::Mat& image);
    
    /**
     * @Description: 获取当前时间字符串
     */
    string getCurrentTimeString();
    
    /**
     * @Description: 记录报警日志
     */
    void logAlarm(const string& region_name, float confidence);

    /**
     * @Description: 在图片上绘制检测框
     * @param {cv::Mat&} image: 要绘制的图片
     * @param {detect_result_t&} detection: 检测结果
     */
    void drawDetectionOnImage(cv::Mat& image, const detect_result_t& detection);

    /**
     * @Description: 计算有效冷却期（考虑渐进式冷却）
     * @param {GlobalAlarmState&} state: 区域状态
     * @return {int}: 有效冷却期（分钟）
     */
    int calculateEffectiveCooldown(const GlobalAlarmState& state);

    /**
     * @Description: 获取无检测持续时间
     * @param {GlobalAlarmState&} state: 区域状态
     * @return {float}: 无检测持续时间（分钟，支持小数）
     */
    float getNoDetectionDuration(const GlobalAlarmState& state);
};

#endif // GLOBAL_ALARM_MANAGER_HPP
