/*
 * @Author: Li RF
 * @Date: 2024-11-26 10:07:30
 * @LastEditors: Li RF
 * @LastEditTime: 2025-03-19 12:56:32
 * @Description: 
 * Email: <EMAIL>
 * Copyright (c) 2024 Li RF, All Rights Reserved.
 */
#ifndef _RKNN_YOLOV5_DEMO_POSTPROCESS_H_
#define _RKNN_YOLOV5_DEMO_POSTPROCESS_H_

#include <stdint.h>
#include <vector>
#include <string>

#define OBJ_NAME_MAX_SIZE 16
#define OBJ_NUMB_MAX_SIZE 64
#define OBJ_CLASS_NUM 80
#define NMS_THRESH 0.45
#define BOX_THRESH 0.25
#define PROP_BOX_SIZE (5 + OBJ_CLASS_NUM)

typedef struct _BOX_RECT
{
    int left;
    int right;
    int top;
    int bottom;
} BOX_RECT;

typedef struct __detect_result_t
{
    char name[OBJ_NAME_MAX_SIZE];
    BOX_RECT box;
    float prop;
} detect_result_t;

typedef struct _detect_result_group_t
{
    int id;
    int count;
    detect_result_t results[OBJ_NUMB_MAX_SIZE];
} detect_result_group_t;

// YOLOv5 后处理函数
int post_process(int8_t *input0, int8_t *input1, int8_t *input2, int model_in_h, int model_in_w,
                 float conf_threshold, float nms_threshold, BOX_RECT pads, float scale_w, float scale_h,
                 std::vector<int32_t> &qnt_zps, std::vector<float> &qnt_scales,
                 detect_result_group_t *group);

// YOLOv8 后处理函数
int post_process_yolov8(void *outputs, int n_output,
                        std::vector<int32_t> &output_zps, std::vector<float> &output_scales,
                        std::vector<std::vector<int>> &output_dims, bool is_quant,
                        int model_in_h, int model_in_w,
                        float conf_threshold, float nms_threshold,
                        float scale_w, float scale_h,
                        detect_result_group_t *group);

// 标签管理函数
int load_labels(const std::string& labels_path);
std::string get_label_name(int class_id);
int get_num_classes();

#endif //_RKNN_YOLOV5_DEMO_POSTPROCESS_H_
