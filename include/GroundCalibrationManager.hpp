/*
 * @Author: Li RF
 * @Date: 2024-12-01 10:00:00
 * @LastEditors: Li RF
 * @LastEditTime: 2024-12-01 10:00:00
 * @Description: 平面标定管理器 - 负责透视变换和目标位置计算
 * Email: <EMAIL>
 * Copyright (c) 2024 Li RF, All Rights Reserved.
 */

#ifndef GROUND_CALIBRATION_MANAGER_HPP
#define GROUND_CALIBRATION_MANAGER_HPP

#include <opencv2/opencv.hpp>
#include <vector>
#include <string>
#include "SharedTypes.hpp"
#include "postprocess.h"

using namespace std;
using namespace cv;

/**
 * @Description: 平面标定管理器
 * 负责透视变换计算和目标位置分析
 */
class GroundCalibrationManager {
private:
    GroundCalibration calibration_config;    // 平面标定配置
    Mat perspective_matrix;                  // 透视变换矩阵
    bool is_calibration_valid;              // 标定是否有效
    int image_width;                        // 图像宽度
    int image_height;                       // 图像高度
    
    // 标定参考区域的真实世界坐标范围
    float real_world_min_x;                 // 真实世界最小X坐标
    float real_world_max_x;                 // 真实世界最大X坐标
    float real_world_min_y;                 // 真实世界最小Y坐标
    float real_world_max_y;                 // 真实世界最大Y坐标

    // 保存原始标定矩形角点（像素坐标，已排序）
    vector<Point2f> calibration_corners;   // 左上、右上、右下、左下
    
public:
    /**
     * @Description: 构造函数
     * @param {int} img_width: 图像宽度
     * @param {int} img_height: 图像高度
     */
    GroundCalibrationManager(int img_width, int img_height);
    
    /**
     * @Description: 析构函数
     */
    ~GroundCalibrationManager();
    
    /**
     * @Description: 加载平面标定配置
     * @param {GroundCalibration&} config: 平面标定配置
     * @return {bool}: 是否加载成功
     */
    bool loadCalibrationConfig(const GroundCalibration& config);
    
    /**
     * @Description: 计算目标位置信息
     * @param {BOX_RECT&} box: 检测框
     * @param {string&} object_name: 目标类型名称
     * @param {float} confidence: 检测置信度
     * @return {TargetPositionInfo}: 目标位置信息
     */
    TargetPositionInfo calculateTargetPosition(const BOX_RECT& box, 
                                              const string& object_name, 
                                              float confidence);
    
    /**
     * @Description: 检查标定是否有效
     * @return {bool}: 标定是否有效
     */
    bool isCalibrationValid() const;
    
private:
    /**
     * @Description: 计算透视变换矩阵
     * @param {GroundRectangle&} rectangle: 标定矩形
     * @return {bool}: 是否计算成功
     */
    bool calculatePerspectiveMatrix(const GroundRectangle& rectangle);
    
    /**
     * @Description: 将像素坐标转换为真实世界坐标
     * @param {Point2f&} pixel_point: 像素坐标点
     * @return {Point2f}: 真实世界坐标点
     */
    Point2f pixelToRealWorld(const Point2f& pixel_point);
    
    /**
     * @Description: 获取检测框底边的两个角点
     * @param {BOX_RECT&} box: 检测框
     * @return {pair<Point2f, Point2f>}: 底边左右两个角点
     */
    pair<Point2f, Point2f> getBoxBottomCorners(const BOX_RECT& box);

    /**
     * @Description: 对矩形角点进行排序（左上、右上、右下、左下）
     * @param {vector<Point2f>&} points: 原始角点
     * @return {vector<Point2f>}: 排序后的角点
     */
    vector<Point2f> sortRectanglePoints(const vector<Point2f>& points);

    /**
     * @Description: 计算指定Y坐标处的地面宽度
     * @param {float} y: Y坐标（像素坐标系）
     * @return {float}: 该Y位置的地面宽度
     */
    float calculateGroundWidthAtY(float y);

    /**
     * @Description: 计算标定矩形在指定Y坐标处的左右边界点
     * @param {float} pixel_y: Y坐标（像素坐标系）
     * @return {pair<float, float>}: 左边界X坐标和右边界X坐标
     */
    pair<float, float> getCalibrationBoundariesAtY(float pixel_y);
};

#endif // GROUND_CALIBRATION_MANAGER_HPP
