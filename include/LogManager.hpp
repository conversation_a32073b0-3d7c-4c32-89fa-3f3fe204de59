/*
 * @Author: Li RF
 * @Date: 2025-01-24 16:30:00
 * @LastEditors: Li RF
 * @LastEditTime: 2025-01-24 16:30:00
 * @Description: 日志管理器 - 单例模式，线程安全，支持文件和控制台输出
 * Email: <EMAIL>
 * Copyright (c) 2025 Li RF, All Rights Reserved.
 */

#ifndef LOG_MANAGER_HPP
#define LOG_MANAGER_HPP

#include <string>
#include <fstream>
#include <mutex>
#include <memory>
#include <thread>
#include <atomic>
#include <chrono>
#include <queue>
#include <condition_variable>
#include "SharedTypes.hpp"

/**
 * @Description: 日志级别枚举
 */
enum class LogLevel {
    DEBUG = 0,
    INFO = 1,
    WARN = 2,
    ERROR = 3
};

/**
 * @Description: 日志条目结构体
 */
struct LogEntry {
    LogLevel level;
    std::string timestamp;
    std::string module;
    std::string message;
    
    LogEntry(LogLevel lvl, const std::string& ts, const std::string& mod, const std::string& msg)
        : level(lvl), timestamp(ts), module(mod), message(msg) {}
};

/**
 * @Description: 日志管理器类（单例模式）
 */
class LogManager {
private:
    static LogManager* instance;
    static std::mutex instance_mutex;
    
    AppConfig config;
    std::ofstream log_file;
    std::mutex log_mutex;
    std::string current_log_file_path;
    std::string current_date;
    std::string instance_identifier;        // 实例标识符
    std::string instance_log_dir;           // 实例专用日志目录
    
    // 异步日志队列
    std::queue<LogEntry> log_queue;
    std::mutex queue_mutex;
    std::condition_variable queue_cv;
    std::thread writer_thread;
    std::atomic<bool> should_stop;
    
    // 心跳日志
    std::thread heartbeat_thread;
    std::atomic<bool> heartbeat_running;
    std::chrono::steady_clock::time_point last_heartbeat;
    
    // 私有构造函数
    LogManager(const AppConfig& app_config);
    
    // 私有方法
    std::string getCurrentTimestamp() const;
    std::string getCurrentDate() const;
    std::string levelToString(LogLevel level) const;
    std::string levelToColorString(LogLevel level) const;
    bool shouldLog(LogLevel level) const;
    void openLogFile();
    void writeToFile(const LogEntry& entry);
    void writeToConsole(const LogEntry& entry);
    std::string generateInstanceId();       // 生成实例标识符
    void createInstanceLogDir();            // 创建实例日志目录
    void cleanupOldLogs();                  // 清理过期日志文件
    void writerThreadFunction();
    void heartbeatThreadFunction();
    LogLevel stringToLogLevel(const std::string& level_str) const;

public:
    ~LogManager();
    
    // 获取实例
    static LogManager* getInstance(const AppConfig& config);
    static LogManager* getInstance();
    static void destroyInstance();
    
    // 日志记录方法
    void log(LogLevel level, const std::string& message, const std::string& module = "");
    void logFormatted(LogLevel level, const std::string& module, const char* format, ...);
    
    // 心跳日志控制
    void startHeartbeat();
    void stopHeartbeat();
    void updateHeartbeat(); // 更新心跳时间
    
    // 工具方法
    void flush(); // 强制刷新所有日志
};

// 便捷的日志宏定义
#define LOG_DEBUG(msg) do { \
    if (LogManager::getInstance()) { \
        LogManager::getInstance()->log(LogLevel::DEBUG, msg, __FUNCTION__); \
    } \
} while(0)

#define LOG_INFO(msg) do { \
    if (LogManager::getInstance()) { \
        LogManager::getInstance()->log(LogLevel::INFO, msg, __FUNCTION__); \
    } \
} while(0)

#define LOG_WARN(msg) do { \
    if (LogManager::getInstance()) { \
        LogManager::getInstance()->log(LogLevel::WARN, msg, __FUNCTION__); \
    } \
} while(0)

#define LOG_ERROR(msg) do { \
    if (LogManager::getInstance()) { \
        LogManager::getInstance()->log(LogLevel::ERROR, msg, __FUNCTION__); \
    } \
} while(0)

// 格式化日志宏
#define LOG_DEBUG_F(format, ...) do { \
    if (LogManager::getInstance()) { \
        LogManager::getInstance()->logFormatted(LogLevel::DEBUG, __FUNCTION__, format, ##__VA_ARGS__); \
    } \
} while(0)

#define LOG_INFO_F(format, ...) do { \
    if (LogManager::getInstance()) { \
        LogManager::getInstance()->logFormatted(LogLevel::INFO, __FUNCTION__, format, ##__VA_ARGS__); \
    } \
} while(0)

#define LOG_WARN_F(format, ...) do { \
    if (LogManager::getInstance()) { \
        LogManager::getInstance()->logFormatted(LogLevel::WARN, __FUNCTION__, format, ##__VA_ARGS__); \
    } \
} while(0)

#define LOG_ERROR_F(format, ...) do { \
    if (LogManager::getInstance()) { \
        LogManager::getInstance()->logFormatted(LogLevel::ERROR, __FUNCTION__, format, ##__VA_ARGS__); \
    } \
} while(0)

#endif // LOG_MANAGER_HPP 