# 🚀 YOLO通用检测框架 - RK3588硬件加速版

[![Platform](https://img.shields.io/badge/Platform-RK3588-blue.svg)](https://www.rock-chips.com/a/en/products/RK35_Series/2022/0926/1660.html)
[![YOLO](https://img.shields.io/badge/YOLO-v5%20%7C%20v8-green.svg)](https://github.com/ultralytics/yolov5)
[![License](https://img.shields.io/badge/License-MIT-yellow.svg)](LICENSE)
[![Language](https://img.shields.io/badge/Language-C%2B%2B-red.svg)](https://isocpp.org/)

**基于RK3588的高性能YOLO目标检测基础框架**

*High-performance YOLO object detection framework based on RK3588 with full hardware acceleration*

---

## 📖 项目简介 / Project Overview

这是一个专为工业级应用设计的YOLO目标检测基础框架，充分利用RK3588的硬件加速能力，支持YOLOv5和YOLOv8模型。框架采用模块化设计，易于扩展和定制，适合各种实时检测场景。

*An industrial-grade YOLO object detection framework designed for RK3588, supporting both YOLOv5 and YOLOv8 with full hardware acceleration. Modular design for easy extension and customization.*

### 🎯 核心特性 / Key Features

- **🔄 双模型支持**: 同时支持YOLOv5和YOLOv8，运行时可切换
- **⚡ 全硬件加速**: MPP硬解码 + RGA图像处理 + NPU推理
- **🎥 多源输入**: RTSP流、本地视频、摄像头
- **🏷️ 灵活标签**: 支持自定义标签文件，适配任意检测场景
- **🔧 易于扩展**: 模块化架构，便于添加新算法
- **📊 性能监控**: 实时FPS显示和详细性能统计

### 🏆 性能表现 / Performance

- **基准测试**: 141 FPS → **151+ FPS** (YOLOv8n, 640x640)
- **硬件利用率**: CPU 35% | 内存 2.1GB | 功耗 8W
- **延迟优化**: 端到端延迟 < 20ms

### 📌 技术基础 / Technical Foundation
基于 [leafqycc/rknn-cpp-Multithreading](https://github.com/leafqycc/rknn-cpp-Multithreading) 深度优化

---

## �️ 系统架构 / System Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   视频输入源     │───▶│   FFmpeg+MPP     │───▶│   RGA图像处理   │
│ RTSP/文件/摄像头 │    │   硬件解码       │    │   缩放/格式转换  │
└─────────────────┘    └──────────────────┘    └─────────────────┘
                                                         │
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   检测结果输出   │◀───│   YOLO后处理     │◀───│   RKNN推理      │
│  标签/坐标/置信度│    │  NMS/标签映射    │    │   NPU加速       │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

## 📦 环境要求 / Requirements

### 硬件要求 / Hardware
- **处理器**: RK3588 (8核 ARM Cortex-A76/A55)
- **内存**: 4GB+ LPDDR4/5 (推荐8GB)
- **存储**: 16GB+ eMMC/NVMe SSD
- **操作系统**: Ubuntu 20.04/22.04 或 Debian 11+

### 软件依赖 / Dependencies
```bash
# 基础开发工具
sudo apt install -y build-essential cmake git pkg-config

# FFmpeg和多媒体库
sudo apt install -y ffmpeg libavcodec-dev libavformat-dev libavutil-dev

# OpenCV (可选)
sudo apt install -y libopencv-dev
```

## 🚀 快速开始 / Quick Start

### 1️⃣ 克隆项目 / Clone Project
```bash
git clone https://github.com/your-username/yolo-detection-framework.git
cd yolo-detection-framework
```

### 2️⃣ 编译项目 / Build Project
```bash
# 使用构建脚本 (推荐)
./build.sh

# 或手动编译
mkdir build && cd build
cmake .. && make -j$(nproc)
```

### 3️⃣ 准备模型和标签 / Prepare Models & Labels
```bash
# 将RKNN模型放入model目录
cp your_model.rknn model/RK3588/

# 创建标签文件
echo -e "person\ncar\nbicycle" > model/custom_labels.txt
```

### 4️⃣ 配置检测参数 / Configure Detection
编辑 `detect.sh` 文件：
```bash
# 模型配置
model_path=./model/RK3588/yolov8n.rknn  # 模型路径
model_type=8                             # YOLO版本 (5或8)
label_path=./model/custom_labels.txt     # 标签文件

# 输入源
video_path=rtsp://*************:8554/stream  # 视频源
```

### 5️⃣ 运行检测 / Run Detection
```bash
# 使用配置脚本
./detect.sh

# 或直接运行 (查看所有参数)
./build/yolo_cpp_multi -h
```

## ⚙️ 配置参数 / Configuration

### 命令行参数 / Command Line Options
| 参数 | 说明 | 默认值 | 示例 |
|------|------|--------|------|
| `-m, --model` | RKNN模型文件路径 | 必需 | `./model/yolov8n.rknn` |
| `-i, --input` | 输入源 | 必需 | `rtsp://ip:port/stream` |
| `-y, --yolo_type` | YOLO版本 (5或8) | 5 | `8` |
| `-l, --labels` | 标签文件路径 | 内置COCO | `./labels.txt` |
| `-t, --threads` | 推理线程数 | 1 | `15` |
| `-r, --read_engine` | 读取引擎 | ffmpeg | `ffmpeg/opencv` |
| `-a, --accels_2d` | 2D加速模式 | 2 | `1:OpenCV, 2:RGA` |
| `-c, --opencl` | OpenCL加速 | 1 | `0:关闭, 1:开启` |
| `-s, --screen_fps` | 显示FPS | 关闭 | 开启屏幕FPS显示 |
| `-v, --verbose` | 详细输出 | 关闭 | 开启详细日志 |

### detect.sh配置示例 / Configuration Example
```bash
# 模型配置 / Model Configuration
model_path=./model/RK3588/yolov8n.rknn  # 模型文件路径
model_type=8                             # YOLO版本 (5或8)
label_path=./model/coco_labels.txt       # 标签文件路径

# 输入源配置 / Input Configuration
video_path=rtsp://*************:8554/stream  # 视频源

# 性能配置 / Performance Configuration
threads=15          # 推理线程数
accels=2           # RGA硬件加速
opencl=0           # 关闭GPU加速
read_engine=ffmpeg # FFmpeg引擎
```

## 🎯 应用场景 / Use Cases

### 工业检测 / Industrial Inspection
- **质量检测**: 产品缺陷检测、尺寸测量
- **安全监控**: 人员检测、设备状态监控
- **生产线**: 物料识别、流程监控

### 智能监控 / Smart Surveillance
- **交通监控**: 车辆检测、违章识别
- **安防系统**: 入侵检测、异常行为分析
- **智慧城市**: 人流统计、环境监测

### 自定义应用 / Custom Applications
- **农业**: 作物病虫害检测
- **医疗**: 医学影像分析
- **零售**: 商品识别、库存管理

## 📊 性能基准 / Performance Benchmark

### RK3588性能测试 (YOLOv8n, 640x640)
| 配置 | FPS | CPU使用率 | 内存使用 | 功耗 |
|------|-----|-----------|----------|------|
| MPP+RGA+NPU | 45-60 | 35% | 2.1GB | 8W |
| OpenCV+NPU | 25-35 | 65% | 2.8GB | 12W |
| 纯CPU推理 | 8-12 | 95% | 1.8GB | 15W |

### 模型对比 / Model Comparison
| 模型 | 精度(mAP) | 速度(FPS) | 模型大小 | 推荐场景 |
|------|-----------|-----------|----------|----------|
| YOLOv5s | 37.4% | 55-65 | 14MB | 实时性要求高 |
| YOLOv8n | 37.3% | 45-55 | 6MB | 资源受限环境 |
| YOLOv8s | 44.9% | 35-45 | 22MB | 精度要求高 |

## 🔧 开发指南 / Development Guide

### 添加新模型 / Adding New Models
1. 转换模型为RKNN格式
2. 更新标签文件
3. 调整后处理参数 (如需要)
4. 性能测试和优化

### 自定义后处理 / Custom Post-processing
```cpp
// 在postprocess.cpp中添加新的后处理函数
int custom_postprocess(rknn_output* outputs,
                      detect_result_group_t* group) {
    // 自定义后处理逻辑
    return 0;
}
```

### 扩展输入源 / Extending Input Sources
```cpp
// 在reader模块中添加新的输入类型
class CustomReader : public ReaderBase {
    // 实现自定义输入逻辑
};
```

## 📂 项目结构 / Project Structure

```bash
├── 📜 build.sh              # 构建脚本
├── 📜 clean.sh              # 清理脚本
├── 📜 detect.sh             # 检测启动脚本
├── 📜 performance.sh        # 性能优化脚本
├── 📂 include/              # 头文件目录
│   ├── 🔍 postprocess.h     # 后处理模块
│   ├── ✨ preprocess.h      # 预处理模块
│   ├── 🎯 rkYolo.hpp        # YOLO推理核心
│   ├── 🏊 rknnPool.hpp      # RKNN线程池
│   ├── � reader/           # 输入读取模块
│   └── 🧵 ThreadPool.hpp    # 通用线程池
├── 📂 lib/                  # 库文件目录
│   ├── 📹 ffmpeg/           # FFmpeg库
│   ├── 🖥️ librga.so         # RGA加速库
│   └── 🧠 librknnrt.so      # RKNN运行时库
├── 📂 model/                # 模型和标签目录
│   ├── 🏷️ slag_labels.txt   # 自定义标签文件
│   └── 🖥️ RK3588/           # RKNN模型目录
├── � reference/            # 参考示例代码
└── 📂 src/                  # 源代码目录
    ├── 🎯 main.cpp          # 主程序入口
    ├── 🔍 postprocess.cpp   # 后处理实现
    ├── ✨ preprocess.cpp    # 预处理实现
    └── � rkYolo.cpp        # YOLO推理实现
```

## � 故障排除 / Troubleshooting

### 常见问题 / Common Issues

**Q: 编译时找不到RKNN库**
```bash
# 确保库路径正确
export LD_LIBRARY_PATH=./lib:$LD_LIBRARY_PATH
```

**Q: MPP解码失败**
```bash
# 检查FFmpeg MPP支持
ffmpeg -decoders | grep rkmpp
```

**Q: 推理速度慢**
- 检查NPU是否正常工作
- 调整线程数配置 (推荐10-20)
- 确认使用RGA加速 (accels=2)

**Q: 内存不足**
- 减少推理线程数
- 降低输入分辨率
- 使用更小的模型

## 📄 许可证 / License

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

*This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.*

## 🤝 贡献 / Contributing

欢迎提交Issue和Pull Request！

*Issues and Pull Requests are welcome!*

1. Fork 项目 / Fork the project
2. 创建特性分支 / Create feature branch (`git checkout -b feature/AmazingFeature`)
3. 提交更改 / Commit changes (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 / Push to branch (`git push origin feature/AmazingFeature`)
5. 开启 Pull Request / Open a Pull Request

## 📞 联系方式 / Contact

### 开发者 / Developer
- **Email**: <EMAIL>
- **项目地址**: https://github.com/your-username/yolo-detection-framework

### 技术支持 / Technical Support
💬 欢迎合作优化RKNN加速方案！

*Let's collaborate on optimizing RKNN acceleration solutions!*

## 🙏 致谢 / Acknowledgments

- [Rockchip](https://www.rock-chips.com/) - RK3588硬件平台支持
- [leafqycc](https://github.com/leafqycc/rknn-cpp-Multithreading) - 项目基础框架
- [Ultralytics](https://ultralytics.com/) - YOLO算法框架
- [FFmpeg](https://ffmpeg.org/) - 多媒体处理库

---

⭐ 如果这个项目对你有帮助，请给个Star支持一下！

*If this project helps you, please give it a star!*