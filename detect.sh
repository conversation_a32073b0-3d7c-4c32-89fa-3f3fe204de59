#!/bin/bash

###
# 系统启动脚本
# 功能：使用YOLO进行实时检测
# 支持：RTSP网络流、本地视频文件、MPP硬件解码、RGA硬件加速
# 硬件平台：RK3588 (8核CPU + Mali GPU + MPP + RGA)
# 作者：Yuwenhao
###

# 遇到错误立即退出
set -e

echo "=== 检测系统启动 ==="

# ==================== 模型配置 ====================
# YOLO模型文件路径选择
# model_path=./model/RK3588/yolov5s-640-640.rknn  # YOLOv5标准模型（80类别COCO数据集）
model_path=./model/RK3588/yolov8n.rknn          # YOLOv8标准模型（80类别COCO数据集）
# model_path=./model/RK3588/slag.rknn               # 定制检测模型（1类别：slag）

# YOLO模型版本选择
# model_path=5  # YOLOv5  速度快
# model_path=8  # YOLOv8  精度高
model_type=8

# 模型标签文件路径
label_path=./model/coco_80_labels_list.txt

# ==================== 输入源配置 ====================
# 视频输入源选择 - 支持本地文件和网络流
# video_path=rtsp://192.168.23.156:8554/jindian         # RTSP网络流（主要检测源）
video_path=rtsp://admin:Yu19991222@@192.168.24.89:554/Streaming/Channels/2  # 其他摄像头

# ==================== 性能配置 ====================
# 推理线程数 - 根据RK3588的8核CPU优化
# 建议值：10-20个线程，平衡CPU使用率和推理速度
threads=15

# ==================== 解码引擎配置 ====================
# 视频读取和解码引擎选择
read_engine=ffmpeg    # FFmpeg引擎（推荐）- 支持MPP硬件解码，性能最佳
#read_engine=opencv   # OpenCV引擎（备用）- 软件解码，兼容性好但性能较低

# ==================== 图像加速配置 ====================
# 2D图像处理加速器选择
# 1: OpenCV软件处理（CPU计算，兼容性好但速度慢）
# 2: RGA硬件加速（RK3588专用，图像缩放/格式转换硬件加速）
accels=1

# ==================== GPU加速配置 ====================
# OpenCL GPU加速开关
# 0: 关闭GPU加速（推荐）- 使用CPU+MPP+RGA组合，稳定性最佳
# 1: 开启GPU加速 - 使用Mali GPU，可能提升性能但稳定性略差
opencl=0

# ==================== 运行时库配置 ====================
# 动态链接库路径设置
# 只保留项目库路径，使用系统FFmpeg库以获得最佳MPP支持
runtime_lib_path=${PWD}/lib

# 设置库搜索路径，优先使用项目库，然后是系统库
export LD_LIBRARY_PATH=$runtime_lib_path:$LD_LIBRARY_PATH

./build/yolo_cpp_multi -m $model_path -i $video_path -t $threads -r $read_engine -s -v -a $accels -c $opencl -y $model_type -l $label_path
